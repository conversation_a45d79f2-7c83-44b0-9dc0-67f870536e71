import { FC, useState } from 'react'
import { Card, Tabs } from 'antd'
import AverageRevenue from './tab-revenue/AverageRevenue'
import CumulativeRevenue from './tab-revenue/CumulativeRevenue'
import GrowthRevenue from './tab-revenue/GrowthRevenue'
import { useTranslation } from 'react-i18next'

export const RevenueTab: FC = () => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('1')

  const items = [
    {
      key: '1',
      label: t(
        'customer:customer.customer_detail.revenue_tab.tabs.averageRevenue'
      ),
      children: <AverageRevenue />
    },
    {
      key: '2',
      label: t(
        'customer:customer.customer_detail.revenue_tab.tabs.cumulativeRevenue'
      ),
      children: <CumulativeRevenue />
    },
    {
      key: '3',
      label: t(
        'customer:customer.customer_detail.revenue_tab.tabs.growthRevenue'
      ),
      children: <GrowthRevenue />
    }
  ]

  return (
    <Card>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={items}
        type='card'
      />
    </Card>
  )
}

import { Card, Row, Col, Select, Tag, Table, Space } from 'antd'
import { Bar } from 'react-chartjs-2'
import {
  Chart,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { getColorPercent, showValueOnBar } from '~/common/utils/common.utils'
import BaseTable from '~/components/BaseTable'

Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend, ArcElement)
Chart.register(showValueOnBar)

const salesKPI = {
  labels: ['KH mới', 'Gọi/gặp', 'Báo giá', '<PERSON><PERSON>h số', 'Chốt đơn', 'CRM'],
  datasets: [
    {
      label: '% Tháng trước',
      backgroundColor: '#B2D8CE',
      data: [20, 50, 30, 30, 20, 95]
    },
    {
      label: '% Tháng hiện tại',
      backgroundColor: '#5459AC',
      data: [105, 80, 103, 8, 75, 62]
    }
  ]
}

const options = {
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  responsive: true,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const { Option } = Select

export const EmployeeSaleKPIChart = () => {
  const columns = [
    {
      title: 'KPI',
      dataIndex: 'kpi',
      key: 'kpi'
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Đơn vị đo',
      dataIndex: 'unit',
      key: 'unit'
    },
    {
      title: 'Mục tiêu',
      dataIndex: 'target',
      key: 'target'
    },
    {
      title: 'Kết quả đạt được',
      dataIndex: 'result',
      key: 'result'
    },
    {
      title: 'Tỷ lệ hoàn thành',
      dataIndex: 'completionRate',
      key: 'completionRate',
      render: (text: string) => {
        const percent = parseInt(text)
        let color = 'green'
        if (percent < 80) color = 'orange'
        if (percent < 60) color = 'red'
        return <Tag color={color}>{text}</Tag>
      }
    },
    {
      title: 'Tổng KPI',
      dataIndex: 'total',
      key: 'total',
      render: (_text: any, _row: any, index: number) => {
        if (index === 0) {
          return {
            children: (
              <Tag color={getColorPercent(81)}>
                <strong>81%</strong>
              </Tag>
            ),
            props: {
              rowSpan: 5 // Số hàng bạn muốn merge (ở đây là 5 dòng)
            }
          }
        }

        return {
          children: null,
          props: {
            rowSpan: 0
          }
        }
      }
    }
  ]

  const data = [
    {
      key: '1',
      kpi: 'Số khách hàng mới',
      description: 'Số lượng khách hàng tạo mới vào hệ thống',
      unit: 'khách hàng/tháng',
      target: 20,
      result: 21,
      completionRate: '105%'
    },
    {
      key: '2',
      kpi: 'Số cuộc gọi / gặp khách',
      description: 'Tổng số lần tương tác (call, meeting, demo)',
      unit: 'lượt',
      target: 50,
      result: 40,
      completionRate: '80%'
    },
    {
      key: '3',
      kpi: 'Số báo giá gửi đi',
      description: 'Gửi qua CRM hoặc email',
      unit: 'báo giá',
      target: 30,
      result: 31,
      completionRate: '103%'
    },
    {
      key: '4',
      kpi: 'Doanh số ký hợp đồng',
      description: 'Tổng giá trị hợp đồng được ký',
      unit: 'VND',
      target: '500.000.000',
      result: '40.000.000',
      completionRate: '8%'
    },
    {
      key: '5',
      kpi: 'Số lượng chốt đơn',
      description: '% cơ hội chuyển thành hợp đồng',
      unit: '%',
      target: 20,
      result: 15,
      completionRate: '75%'
    },
    {
      key: '6',
      kpi: 'Cập nhật CRM',
      description: '% số lượng deal có đầy đủ thông tin, trạng thái, ghi chú',
      unit: '%',
      target: 95,
      result: 59,
      completionRate: '62%'
    }
  ]

  return (
    <Card title='KPI nhóm nhân viên kinh doanh' size='small'>
      {/* Chọn tháng trước cần xem */}
      <Space>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn năm'>
          <Option value=''>--Chọn năm--</Option>
          <Option value='2021'>2021</Option>
          <Option value='2022'>2022</Option>
          <Option value='2023'>2023</Option>
        </Select>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn quý'>
          <Option value=''>--Chọn quý--</Option>
          <Option value='1'>Quý 1</Option>
          <Option value='2'>Quý 2</Option>
          <Option value='3'>Quý 3</Option>
          <Option value='3'>Quý 4</Option>
        </Select>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn tháng trước'>
          <Option value=''>--Chọn tháng--</Option>
          <Option value='1'>Tháng 1</Option>
          <Option value='2'>Tháng 2</Option>
          <Option value='3'>Tháng 3</Option>
          <Option value='3'>Tháng 4</Option>
          <Option value='3'>Tháng 5</Option>
        </Select>
      </Space>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <div
            style={{
              width: '100%',
              height: '425px',
              padding: '20px',
              backgroundColor: '#fff',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
            <Bar data={salesKPI} options={options} />
          </div>
        </Col>
        <Col xs={24} lg={12}>
          <Table
            style={{ height: '450px' }}
            size='small'
            columns={columns}
            dataSource={data}
            pagination={false}
            bordered
          />
        </Col>
      </Row>
    </Card>
  )
}

export interface ICustomerCriteria {
  id: string
  key: string
  name: string
  description: string
  status: string
  createdAt: string
  updatedAt: string
}

export interface ICustomerCriteriaResponse {
  data: ICustomerCriteria[]
  total: number
}

export interface ICustomerCriteriaFilter {
  pageIndex: number
  pageSize: number
  key: string
  name: string
}

export interface ICustomerCriteriaResponse {
  data: ICustomerCriteria[]
  total: number
  pageIndex: number
  pageSize: number
}

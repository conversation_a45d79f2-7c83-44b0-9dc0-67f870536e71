import { EditOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons'
import { Card, Row, Col, Form, Input, Button, Select, DatePicker } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'

import { IEmployee } from '~/dto/employee.dto'
import moment from 'moment'
import { useEffect } from 'react'
import { useModal } from '~/hooks/useModal'
import { Invoice } from '..'

const { Option } = Select
const { TextArea } = Input

interface EditButtonProps {
  data: Invoice
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()
  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data
      })
    }
  }, [open, data, form])

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form layout='vertical' form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='Mã hóa đơn' name='code' required>
                <Input placeholder='Nhập mã hóa đơn' />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label='Đơn vị phát hành' name='issuingUnit' required>
                <Select>
                  <Option value='MISA'>MISA</Option>
                  <Option value='VNPT Invoice'>VNPT Invoice</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Tên khách hàng' name='customerName' required>
                <Input placeholder='Nhập tên khách hàng áp dụng' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Loại hình dịch vụ' name='serviceType' required>
                <Select>
                  <Option value='Logistic'>Vận chuyển</Option>
                  <Option value='Other'>Khác</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </div>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type='primary' tooltip='Chỉnh sửa' />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa mẫu hóa đơn'
        description='Cập nhật thông tin mẫu hóa đơn'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

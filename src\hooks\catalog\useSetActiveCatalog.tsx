import { useMutation, useQueryClient } from '@tanstack/react-query'
import { rootApiService, toastService } from '~/services/@common'
import { CreateProductReq } from '~/dto/product.dto'
import { endpoint_catalog } from '~/services/endpoints'
import { useListCatalog } from './useListCatalog'

//createCatalog

export interface SetActiveCatalogReq {
  id: string
}

export const useSetActiveCatalog = () => {
  const { refetch } = useListCatalog({})

  return useMutation({
    mutationFn: (body: SetActiveCatalogReq) => rootApiService.post(endpoint_catalog.active, body),
    onSuccess: () => {
      refetch()
      toastService.success('Cập nhật trạng thái hoạt động thành công')
    },
    onError: (error) => {
      toastService.error('Cập nhật trạng thái hoạt động thất bại')
    }
  })
}

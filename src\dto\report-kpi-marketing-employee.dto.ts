export interface ReportKpiMarketingEmployee {
  stt: number
  code: string // employee code
  name: string
  campaignsTarget: number
  campaignsActual: number
  campaignsRate: string
  reachTarget: number
  reachActual: number
  reachRate: string
  conversionRateTarget: string
  conversionRateActual: string
  conversionRateRate: string
  costPerLeadTarget: number
  costPerLeadActual: number
  costPerLeadRate: string
  leadsTarget: number
  leadsActual: number
  leadsRate: string
  totalKpi: string
}

export interface ReportKpiMarketingEmployeeResponse {
  data: ReportKpiMarketingEmployee[]
  total: number
}

//filter

export interface IFilterReportKpiMarketingEmployee {
  code?: string
  name?: string
  month?: string
  pageIndex?: number
  pageSize?: number
}

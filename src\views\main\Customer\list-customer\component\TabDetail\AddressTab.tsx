import { FC } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface IAddress {
  id: string
  addressType: string
  market: string
  area: string
  address: string
  areaSize: number
  note: string
  isMain: boolean
  createdBy: string
  createdAt: string
}

type IProps = {}

const dummyData: IAddress[] = [
  {
    id: '1',
    addressType: 'Văn phòng',
    market: 'Miền Nam',
    area: 'Miền Nam',
    address: '123 Đ<PERSON>ờng Lê <PERSON>, Phường Bến Nghé, Quận 1, TP.HCM',
    areaSize: 150,
    note: 'Tầng 5, Tòa nhà A',
    isMain: true,
    createdBy: 'Nguyễn Văn A',
    createdAt: '2024-03-20T08:00:00'
  },
  {
    id: '2',
    addressType: '<PERSON><PERSON><PERSON> hàng',
    market: '<PERSON><PERSON><PERSON>',
    area: '<PERSON><PERSON><PERSON>',
    address:
      '456 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Quận 2, TP.HCM',
    areaSize: 80,
    note: 'Mặt tiền đường',
    isMain: false,
    createdBy: 'Trần Thị B',
    createdAt: '2024-03-19T09:30:00'
  },
  {
    id: '3',
    addressType: 'Kho',
    market: 'Miền Bắc',
    area: 'Miền Bắc',
    address:
      '789 Đường Nguyễn Văn Linh, Phường Thạch Bàn, Quận Long Biên, Hà Nội',
    areaSize: 500,
    note: 'Khu công nghiệp',
    isMain: false,
    createdBy: 'Lê Văn C',
    createdAt: '2024-03-18T10:15:00'
  },
  {
    id: '4',
    addressType: 'Văn phòng',
    market: 'Miền Trung',
    area: 'Miền Trung',
    address:
      '321 Đường Nguyễn Văn Linh, Phường Nam Dương, Quận Hải Châu, Đà Nẵng',
    areaSize: 120,
    note: 'Tầng 3, Tòa nhà B',
    isMain: false,
    createdBy: 'Phạm Thị D',
    createdAt: '2024-03-17T14:20:00'
  },
  {
    id: '5',
    addressType: 'Cửa hàng',
    market: 'Miền Nam',
    area: 'Miền Nam',
    address: '654 Đường Nguyễn Thị Thập, Phường Tân Phú, Quận 7, TP.HCM',
    areaSize: 100,
    note: 'Gần chợ',
    isMain: false,
    createdBy: 'Hoàng Văn E',
    createdAt: '2024-03-16T16:45:00'
  }
]

export const AddressTab: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  const columns: ColumnsType<IAddress> = [
    {
      title: t('customer:customer.customer_detail.address_tab.columns.stt'),
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: t(
        'customer:customer.customer_detail.address_tab.columns.addressType'
      ),
      dataIndex: 'addressType',
      key: 'addressType',
      width: 120
    },
    // {
    //   title: t('customer:customer.customer_detail.address_tab.columns.market'),
    //   dataIndex: 'market',
    //   key: 'market',
    //   width: 120
    // },
    {
      title: t('customer:customer.customer_detail.address_tab.columns.area'),
      dataIndex: 'area',
      key: 'area',
      width: 120
    },
    {
      title: t('customer:customer.customer_detail.address_tab.columns.address'),
      dataIndex: 'address',
      key: 'address',
      width: 300
    },
    {
      title: t(
        'customer:customer.customer_detail.address_tab.columns.areaSize'
      ),
      dataIndex: 'areaSize',
      key: 'areaSize',
      width: 100,
      render: (value: number) => `${value} m²`
    },
    {
      title: t('customer:customer.customer_detail.address_tab.columns.note'),
      dataIndex: 'note',
      key: 'note',
      width: 150
    },
    {
      title: t('customer:customer.customer_detail.address_tab.columns.isMain'),
      dataIndex: 'isMain',
      key: 'isMain',
      width: 120,
      render: (value: boolean) => (
        <Tag
          color={value ? 'success' : 'default'}
          style={{ fontSize: '14px', padding: '4px 12px' }}>
          {value ? 'Có' : 'Không'}
        </Tag>
      )
    },
    {
      title: t(
        'customer:customer.customer_detail.address_tab.columns.createdBy'
      ),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150
    },
    {
      title: t(
        'customer:customer.customer_detail.address_tab.columns.createdAt'
      ),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (value: string) =>
        value ? new Date(value).toLocaleDateString('vi-VN') : '-'
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={dummyData}
            total={dummyData.length}
            isLoading={false}
            scroll={{ x: 2000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

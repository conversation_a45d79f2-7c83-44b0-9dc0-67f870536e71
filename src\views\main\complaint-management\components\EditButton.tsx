import { EditOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons'
import {
  Card,
  Tag,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Upload,
  message,
  DatePicker
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { IProduct } from '~/dto/product.dto'
import { EProduct } from '~/common/enums/NSProduct'
import { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'
import {
  useUpdateProduct,
  UseUpdateProductParams
} from '~/hooks/product/useUpdateProduct'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import type { UploadFile, UploadProps } from 'antd'
import { toastService } from '~/services/@common'
import { useDetailProduct } from '~/hooks/product/useDetailProduct'
import { FC } from 'react'
import {
  IComplaint,
  EComplaintType,
  EComplaintPriority,
  EComplaintStatus,
  ENotificationStatus,
  ComplaintTypeLabels,
  ComplaintPriorityLabels,
  ComplaintStatusLabels,
  ComplaintStatusColors,
  ComplaintPriorityColors,
  NotificationStatusLabels
} from '~/dto/complaint.dto'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface IProps {
  data: IComplaint
}

const EditButton: FC<IProps> = ({ data }: IProps) => {
  const { id } = data
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const { mutateAsync: updateProduct, isPending } = useUpdateProduct()
  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } =
    useUploadSingle()

  const {
    title,
    description,
    status,
    type,
    customerId,
    complaintAddress,
    address,
    provinceCode,
    districtCode,
    wardCode,
    supervisorId,
    assignedStaffId,
    startDate,
    dueDate,
    endDate,
    checkinTime,
    checkoutTime,
    notes,
    solution,
    rating,
    feedback,
    media
  } = data || {}

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        title: data.title,
        description: data.description,
        type: data.type,
        priority: data.priority,
        customerId: data.customerId,
        complaintAddress: data.complaintAddress,
        address: data.address,
        provinceCode: data.provinceCode,
        districtCode: data.districtCode,
        wardCode: data.wardCode,
        supervisorId: data.supervisorId,
        assignedStaffId: data.assignedStaffId,
        startDate: data.startDate ? dayjs(data.startDate) : null,
        dueDate: data.dueDate ? dayjs(data.dueDate) : null,
        endDate: data.endDate ? dayjs(data.endDate) : null,
        checkinTime: data.checkinTime ? dayjs(data.checkinTime) : null,
        checkoutTime: data.checkoutTime ? dayjs(data.checkoutTime) : null,
        notes: data.notes,
        solution: data.solution,
        rating: data.rating,
        feedback: data.feedback
      })

      // Set existing images to fileList
      if (data.media && data.media.length > 0) {
        const existingFiles: UploadFile[] = data.media.map((media, index) => ({
          uid: media.id || `-${index}`,
          name: media.name || `image-${index + 1}`,
          status: 'done',
          url: media.url
        }))
        setFileList(existingFiles)
      } else {
        setFileList([])
      }
    }
  }, [data, open, form])

  if (!data) return null

  const handleSave = async (values: any) => {
    if (!data) return
    const body = {
      ...values,
      id: data.id,
      startDate: values.startDate?.format('YYYY-MM-DD'),
      dueDate: values.dueDate?.format('YYYY-MM-DD'),
      endDate: values.endDate?.format('YYYY-MM-DD'),
      checkinTime: values.checkinTime?.format('YYYY-MM-DD HH:mm:ss'),
      checkoutTime: values.checkoutTime?.format('YYYY-MM-DD HH:mm:ss'),
      media: fileList.map((file) => file.url)
    }

    try {
      // TODO: Implement update complaint API call
      closeModal()
      // Reset form and file list
      form.resetFields()
      setFileList([])
    } catch (error) {
      toastService.error('Cập nhật khiếu nại thất bại')
    }
  }

  // Handle image upload
  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const handleRemove = (file: UploadFile) => {
    setFileList((prev) => prev.filter((f) => f.uid !== file.uid))
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!')
    }
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      message.error('Hình ảnh phải nhỏ hơn 2MB!')
    }
    return isImage && isLt2M
  }

  const uploadButton = (
    <div>
      <UploadOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  // Header thông tin khiếu nại
  const complaintHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>Tiêu đề:</BaseText>
            <br />
            <BaseText weight='bold'>{data.title || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>Loại khiếu nại:</BaseText>
            <br />
            <Tag color={ComplaintStatusColors[data.status]}>
              {ComplaintTypeLabels[data.type]}
            </Tag>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>Trạng thái:</BaseText>
            <br />
            <Tag color={ComplaintStatusColors[data.status]}>
              {ComplaintStatusLabels[data.status]}
            </Tag>
          </div>
        </Col>
      </Row>
      <Row style={{ marginTop: 12 }}>
        <Col span={8}>
          <BaseText color='textSecondary'>Mức độ ưu tiên:</BaseText>
          <br />
          <Tag color={ComplaintPriorityColors[data.priority]}>
            {ComplaintPriorityLabels[data.priority]}
          </Tag>
        </Col>
        <Col span={8}>
          <BaseText color='textSecondary'>Hình ảnh:</BaseText>
          <br />
          <BaseText weight='bold'>{data.media?.length || 0} ảnh</BaseText>
        </Col>
      </Row>
    </Card>
  )

  const modalContent = (
    <div>
      {complaintHeader}

      <Form form={form} layout='vertical' onFinish={handleSave}>
        {/* Thông tin cơ bản */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Tiêu đề'
              name='title'
              rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}>
              <Input placeholder='Nhập tiêu đề khiếu nại' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Loại khiếu nại'
              name='type'
              rules={[
                { required: true, message: 'Vui lòng chọn loại khiếu nại' }
              ]}>
              <Select placeholder='Chọn loại khiếu nại'>
                {Object.entries(ComplaintTypeLabels).map(([value, label]) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label='Mô tả'
              name='description'
              rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}>
              <TextArea rows={3} placeholder='Nhập mô tả chi tiết khiếu nại' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Mức độ ưu tiên'
              name='priority'
              rules={[
                { required: true, message: 'Vui lòng chọn mức độ ưu tiên' }
              ]}>
              <Select placeholder='Chọn mức độ ưu tiên'>
                {Object.entries(ComplaintPriorityLabels).map(
                  ([value, label]) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  )
                )}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Mã khách hàng'
              name='customerId'
              rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}>
              <Select placeholder='Chọn khách hàng'>
                {/* TODO: Add customer options */}
                <Option value='1'>Công ty TNHH ABC</Option>
                <Option value='2'>Công ty TNHH XYZ</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin địa chỉ */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Địa chỉ khiếu nại'
              name='complaintAddress'
              rules={[
                { required: true, message: 'Vui lòng nhập địa chỉ khiếu nại' }
              ]}>
              <Input placeholder='Nhập địa chỉ khiếu nại' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Địa chỉ'
              name='address'
              rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}>
              <Input placeholder='Nhập địa chỉ' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label='Tỉnh/Thành phố' name='provinceCode'>
              <Select placeholder='Chọn tỉnh/thành phố'>
                {/* TODO: Add province options */}
                <Option value='01'>Hồ Chí Minh</Option>
                <Option value='02'>Hà Nội</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Quận/Huyện' name='districtCode'>
              <Select placeholder='Chọn quận/huyện'>
                {/* TODO: Add district options */}
                <Option value='001'>Quận 1</Option>
                <Option value='002'>Quận 2</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Phường/Xã' name='wardCode'>
              <Select placeholder='Chọn phường/xã'>
                {/* TODO: Add ward options */}
                <Option value='00001'>Phường 1</Option>
                <Option value='00002'>Phường 2</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin nhân viên */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='NV theo dõi/giám sát'
              name='supervisorId'
              rules={[
                { required: true, message: 'Vui lòng chọn nhân viên giám sát' }
              ]}>
              <Select placeholder='Chọn nhân viên giám sát'>
                {/* TODO: Add supervisor options */}
                <Option value='1'>Nguyễn Văn A</Option>
                <Option value='2'>Trần Thị B</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='NV được phân công'
              name='assignedStaffId'
              rules={[
                {
                  required: true,
                  message: 'Vui lòng chọn nhân viên được phân công'
                }
              ]}>
              <Select placeholder='Chọn nhân viên được phân công'>
                {/* TODO: Add assigned staff options */}
                <Option value='1'>Lê Văn C</Option>
                <Option value='2'>Phạm Thị D</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin thời gian */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Ngày bắt đầu'
              name='startDate'
              rules={[
                { required: true, message: 'Vui lòng chọn ngày bắt đầu' }
              ]}>
              <DatePicker
                style={{ width: '100%' }}
                placeholder='Chọn ngày bắt đầu'
                format='DD/MM/YYYY'
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Ngày hết hạn'
              name='dueDate'
              rules={[
                { required: true, message: 'Vui lòng chọn ngày hết hạn' }
              ]}>
              <DatePicker
                style={{ width: '100%' }}
                placeholder='Chọn ngày hết hạn'
                format='DD/MM/YYYY'
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Ngày kết thúc' name='endDate'>
              <DatePicker
                style={{ width: '100%' }}
                placeholder='Chọn ngày kết thúc'
                format='DD/MM/YYYY'
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Thời gian check-in' name='checkinTime'>
              <DatePicker
                style={{ width: '100%' }}
                placeholder='Chọn thời gian check-in'
                format='DD/MM/YYYY HH:mm:ss'
                showTime
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Thời gian check-out' name='checkoutTime'>
              <DatePicker
                style={{ width: '100%' }}
                placeholder='Chọn thời gian check-out'
                format='DD/MM/YYYY HH:mm:ss'
                showTime
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Thông tin giải pháp và đánh giá */}
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label='Ghi chú' name='notes'>
              <TextArea rows={2} placeholder='Nhập ghi chú' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label='Giải pháp' name='solution'>
              <TextArea rows={2} placeholder='Nhập giải pháp' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Đánh giá' name='rating'>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={5}
                placeholder='Nhập đánh giá (0-5)'
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Phản hồi' name='feedback'>
              <TextArea rows={2} placeholder='Nhập phản hồi' />
            </Form.Item>
          </Col>
        </Row>

        {/* Hình ảnh đính kèm */}
        <Form.Item label='Hình ảnh đính kèm' name='media'>
          <Upload
            listType='picture-card'
            fileList={fileList}
            beforeUpload={beforeUpload}
            multiple
            onChange={handleUploadChange}
            onRemove={handleRemove}
            accept='image/*'
            customRequest={async ({ file, onSuccess }) => {
              try {
                const formData = new FormData()
                formData.append('file', file as File)

                await uploadSingle(formData).then((res) => {
                  handleUploadChange(res)
                })
              } catch (error) {
                console.log('======uploadSingle=====>>', error)
              }
            }}>
            {fileList.length >= 8 ? null : uploadButton}
          </Upload>
          <div style={{ color: '#666', fontSize: '12px', marginTop: '8px' }}>
            Tối đa 8 hình ảnh, mỗi file nhỏ hơn 2MB
          </div>
        </Form.Item>

        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button
            type='primary'
            htmlType='submit'
            icon={<SaveOutlined />}
            loading={isUploadingSingle}>
            Cập nhật khiếu nại
          </Button>
        </div>
      </Form>
    </div>
  )

  return (
    <>
      <BaseButton
        type='primary'
        shape='circle'
        icon={<EditOutlined />}
        tooltip='Edit'
        onClick={openModal}
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa khiếu nại'
        description='Cập nhật thông tin khiếu nại'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

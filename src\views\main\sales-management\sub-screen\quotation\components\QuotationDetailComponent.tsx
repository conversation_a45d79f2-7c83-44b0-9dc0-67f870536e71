import React from 'react'
import { Card, Row, Col, Descriptions, Table, Typography } from 'antd'

const { Text } = Typography

// <PERSON>h sách chi tiết dịch vụ
const serviceData = [
  {
    key: '1',
    stt: 1,
    service: '<PERSON><PERSON> vận chuyển cơ bản',
    description: 'Từ Hà Nội đến TP.HCM',
    unit: 'Chuyến',
    quantity: 1,
    unitPrice: 1500000,
    amount: 1500000
  },
  {
    key: '2',
    stt: 2,
    service: '<PERSON><PERSON> đóng gói',
    description: 'Bao bì, kiện hàng',
    unit: 'Gói',
    quantity: 1,
    unitPrice: 200000,
    amount: 200000
  },
  {
    key: '3',
    stt: 3,
    service: '<PERSON><PERSON> bảo hiểm',
    description: 'Hàng hóa trong quá trình vận chuyển',
    unit: 'Gói',
    quantity: 1,
    unitPrice: 100000,
    amount: 100000
  },
  {
    key: '4',
    stt: 4,
    service: '<PERSON><PERSON> lư<PERSON> kho',
    description: 'Tại điểm trung chuyển',
    unit: 'Ngày',
    quantity: 1,
    unitPrice: 50000,
    amount: 50000
  }
]

// Tính tổng phí
const total = serviceData.reduce((sum, item) => sum + item.amount, 0)
const vat = total * 0.1
const grandTotal = total + vat

const mockData = {
  customer: 'Công ty ABC',
  fromAddress: 'Hà Nội',
  toAddress: 'TP. Hồ Chí Minh',
  goodsType: 'Hàng khô',
  weight: 500,
  packageCount: 25,
  transportMethod: 'Đường bộ',
  baseFee: 1500000,
  packingFee: 200000,
  insuranceFee: 100000,
  storageFee: 50000,
  totalFee: total,
  deliveryTime: '3 ngày',
  paymentTerm: 'Thanh toán trong 7 ngày sau giao hàng'
}

export const QuotationDetailComponent = () => {
  const columns = [
    {
      title: 'STT',
      dataIndex: 'stt',
      width: 60
    },
    {
      title: 'Dịch vụ',
      dataIndex: 'service',
      render: (text: string, record: any) => (
        <>
          <Text strong>{text}</Text>
          <br />
          <Text type='secondary' italic>
            {record.description}
          </Text>
        </>
      )
    },
    {
      title: 'ĐVT',
      dataIndex: 'unit',
      width: 80
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      width: 80
    },
    {
      title: 'Đơn giá (VNĐ)',
      dataIndex: 'unitPrice',
      align: 'right' as const,
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Thành tiền (VNĐ)',
      dataIndex: 'amount',
      align: 'right' as const,
      render: (value: number) => value.toLocaleString()
    }
  ]

  return (
    <Card title={`Chi tiết báo giá [BG-2025-057]`}>
      <Descriptions column={2} bordered size='middle' style={{ marginTop: '16px' }}>
        <Descriptions.Item label='Mẫu báo giá'>TBG-01</Descriptions.Item>
        <Descriptions.Item label='Mẫu dịch vụ'>Vận tải - Logistics</Descriptions.Item>
        <Descriptions.Item label='Ngày báo giá'>17/06/2025</Descriptions.Item>
        <Descriptions.Item label='Khách hàng'>{mockData.customer}</Descriptions.Item>
        <Descriptions.Item label='Điện thoại'>0903 999 999</Descriptions.Item>
        <Descriptions.Item label='Email'><EMAIL></Descriptions.Item>
        <Descriptions.Item label='Loại dịch vụ'>{mockData.goodsType}</Descriptions.Item>

        <Descriptions.Item label='Địa chỉ gửi hàng'>{mockData.fromAddress}</Descriptions.Item>
        <Descriptions.Item label='Địa chỉ nhận hàng'>{mockData.toAddress}</Descriptions.Item>

        <Descriptions.Item label='Khối lượng (kg)'>{mockData.weight}</Descriptions.Item>
        <Descriptions.Item label='Số lượng kiện'>{mockData.packageCount}</Descriptions.Item>

        <Descriptions.Item label='Phương thức vận chuyển'>{mockData.transportMethod}</Descriptions.Item>
        <Descriptions.Item label='Thời gian giao hàng'>{mockData.deliveryTime}</Descriptions.Item>

        <Descriptions.Item label='Phí vận chuyển cơ bản'>{mockData.baseFee.toLocaleString()} ₫</Descriptions.Item>
        <Descriptions.Item label='Phí đóng gói'>{mockData.packingFee.toLocaleString()} ₫</Descriptions.Item>
        <Descriptions.Item label='Phí bảo hiểm'>{mockData.insuranceFee.toLocaleString()} ₫</Descriptions.Item>
        <Descriptions.Item label='Phí lưu kho'>{mockData.storageFee.toLocaleString()} ₫</Descriptions.Item>

        <Descriptions.Item label='Tổng phí' span={2}>
          <strong>{mockData.totalFee.toLocaleString()} ₫</strong>
        </Descriptions.Item>

        <Descriptions.Item label='Điều kiện thanh toán' span={2}>
          {mockData.paymentTerm}
        </Descriptions.Item>
      </Descriptions>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} sm={12}>
          <Card title='Chi tiết dịch vụ báo giá'>
            <Table
              columns={columns}
              dataSource={serviceData}
              pagination={false}
              bordered
              summary={() => (
                <>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={5} align='right'>
                      <Text strong>Tổng cộng (chưa VAT)</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align='right'>
                      {total.toLocaleString()}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={5} align='right'>
                      <Text strong>Thuế GTGT (10%)</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align='right'>
                      {vat.toLocaleString()}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={5} align='right'>
                      <Text strong>Tổng thanh toán</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align='right'>
                      <Text strong>{grandTotal.toLocaleString()}</Text>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </>
              )}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12}>
          <Card title='Điều khoản thanh toán'>
            <p>- Thanh toán bằng chuyển khoản trong vòng 07 ngày kể từ ngày hoàn tất giao hàng</p>
            <p>- Báo giá có hiệu lực trong vòng 15 ngày kể từ ngày phát hành</p>
            <p>- Giá chưa bao gồm chi phí phát sinh ngoài hợp đồng (nếu có)</p>
            <p>- Giá đã bao gồm phí cầu đường, xăng dầu</p>
          </Card>

          <Descriptions
            column={1}
            bordered
            size='middle'
            title='Thông tin nhận chuyển khoản'
            style={{ marginTop: '16px' }}>
            <Descriptions.Item label='Ngân hàng'>Vietcombank – CN TP.HCM</Descriptions.Item>
            <Descriptions.Item label='Chủ tài khoản'>CÔNG TY TNHH ABC LOGISTICS</Descriptions.Item>
            <Descriptions.Item label='Số tài khoản'>0123 456 789</Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </Card>
  )
}

import BaseView from '~/components/BaseView'
import { useTranslation } from 'react-i18next'
import { IKpiCategoryFilter, IKpiCategory, IKpiEvaluate, IKpiEvaluateFilter } from '~/dto/Kpi.dto'
import { Tag } from 'antd'
import { ColumnsType } from 'antd/es/table'
import BaseTable from '~/components/BaseTable'
import { DeleteOutlined } from '@ant-design/icons'

import { BaseButton } from '~/components'
import DetailButton from './components/DetailButton'
import { useState } from 'react'
import FilterKPICategory from './components/FilterKPIEvaluation' //filter

export const EvaluateKPIView = () => {
  const { t } = useTranslation()
  //filter
  const [filter, setFilter] = useState<IKpiEvaluateFilter>({
    pageIndex: 0,
    pageSize: 10
  })

  const [fakeData, setFakeData] = useState<IKpiEvaluate[]>([
    {
      code: 'KPI01',
      evaluateMonth: '6',
      kpiGroup: 'KPI NV KD',
      department: 'Kinh doanh',
      departmentData: [
        //IReportKpiSaleEmployee
        {
          key: '1',
          name: 'Nguyễn Văn A',
          code: 'NV0001',
          newCustomersTarget: 10,
          newCustomersActual: 12,
          newCustomersRate: 120,
          callsTarget: 50,
          callsActual: 55,
          callsRate: 110,
          quotesTarget: 30,
          quotesActual: 35,
          quotesRate: 117,
          ordersTarget: 30,
          ordersActual: 35,
          ordersRate: 117,
          revenueTarget: 500000000,
          revenueActual: 600000000,
          revenueRate: 120,
          closingRateTarget: 10,
          closingRateActual: 11,
          closingRateRate: 110,
          crmUpdateTarget: 90,
          crmUpdateActual: 92,
          crmUpdateRate: 102,
          totalKpi: 110.85
        },
        {
          key: '2',
          name: 'Trần Thị B',
          code: 'NV0002',
          newCustomersTarget: 10,
          newCustomersActual: 8,
          newCustomersRate: 80,
          callsTarget: 50,
          callsActual: 40,
          callsRate: 80,
          quotesTarget: 30,
          quotesActual: 25,
          quotesRate: 83,
          ordersTarget: 30,
          ordersActual: 25,
          ordersRate: 83,
          revenueTarget: 500000000,
          revenueActual: 300000000,
          revenueRate: 60,
          closingRateTarget: 10,
          closingRateActual: 8,
          closingRateRate: 80,
          crmUpdateTarget: 90,
          crmUpdateActual: 85,
          crmUpdateRate: 94,
          totalKpi: 86.56
        },
        {
          key: '3',
          name: 'Lê Hoàng C',
          code: 'NV0003',
          newCustomersTarget: 10,
          newCustomersActual: 11,
          newCustomersRate: 110,
          callsTarget: 50,
          callsActual: 55,
          callsRate: 110,
          quotesTarget: 30,
          quotesActual: 35,
          quotesRate: 117,
          ordersTarget: 30,
          ordersActual: 35,
          ordersRate: 117,
          revenueTarget: 500000000,
          revenueActual: 600000000,
          revenueRate: 120,
          closingRateTarget: 10,
          closingRateActual: 11,
          closingRateRate: 110,
          crmUpdateTarget: 90,
          crmUpdateActual: 92,
          crmUpdateRate: 102,
          totalKpi: 110.85
        },
        {
          key: '4',
          name: 'Phạm Mai D',
          code: 'NV0004',
          newCustomersTarget: 10,
          newCustomersActual: 12,
          newCustomersRate: 120,
          callsTarget: 50,
          callsActual: 55,
          callsRate: 110,
          quotesTarget: 30,
          quotesActual: 35,
          quotesRate: 117,
          ordersTarget: 30,
          ordersActual: 35,
          ordersRate: 117,
          revenueTarget: 500000000,
          revenueActual: 600000000,
          revenueRate: 120,
          closingRateTarget: 10,
          closingRateActual: 11,
          closingRateRate: 110,
          crmUpdateTarget: 90,
          crmUpdateActual: 92,
          crmUpdateRate: 102,
          totalKpi: 110.85
        }
      ],
      unit: 'khách hàng',
      createdBy: 'Nguyễn Văn Bình',
      createdDate: '19/06/2025',
      approvedBy: 'ADMIN',
      approvedDate: '19/06/2025',
      status: 'Chờ duyệt'
    },
    {
      code: 'KPI02',
      evaluateMonth: '6',
      kpiGroup: 'KPI NV MKT',
      department: 'Marketing',
      departmentData: [
        //IReportKpiMarketingEmployee

        {
          key: '1',
          name: 'Nguyễn Văn Y',
          code: 'NV0001',
          campaignsTarget: 3,
          campaignsActual: 4,
          campaignsRate: 133,
          reachTarget: 10,
          reachActual: 9,
          reachRate: 90,
          conversionRateTarget: 5,
          conversionRateActual: 4.5,
          conversionRateRate: 90,
          costPerLeadTarget: 100000,
          costPerLeadActual: 85000,
          costPerLeadRate: 85,
          leadsTarget: 100,
          leadsActual: 110,
          leadsRate: 110,
          totalKpi: 102.85
        },
        {
          key: '2',
          name: 'Trần Thị K',
          code: 'NV0002',
          campaignsTarget: 3,
          campaignsActual: 2,
          campaignsRate: 67,
          reachTarget: 10,
          reachActual: 8,
          reachRate: 80,
          conversionRateTarget: 5,
          conversionRateActual: 4,
          conversionRateRate: 80,
          costPerLeadTarget: 100000,
          costPerLeadActual: 110000,
          costPerLeadRate: 110,
          leadsTarget: 100,
          leadsActual: 95,
          leadsRate: 95,
          totalKpi: 84.5
        },
        {
          key: '3',
          name: 'Lê Hoàng L',
          code: 'NV0003',
          campaignsTarget: 3,
          campaignsActual: 3,
          campaignsRate: 100,
          reachTarget: 10,
          reachActual: 11,
          reachRate: 110,
          conversionRateTarget: 5,
          conversionRateActual: 5.2,
          conversionRateRate: 104,
          costPerLeadTarget: 100000,
          costPerLeadActual: 98000,
          costPerLeadRate: 98,
          leadsTarget: 100,
          leadsActual: 115,
          leadsRate: 115,
          totalKpi: 106.85
        },
        {
          key: '4',
          name: 'Phạm Mai M',
          code: 'NV0004',
          campaignsTarget: 3,
          campaignsActual: 3,
          campaignsRate: 100,
          reachTarget: 10,
          reachActual: 11,
          reachRate: 110,
          conversionRateTarget: 5,
          conversionRateActual: 5.2,
          conversionRateRate: 104,
          costPerLeadTarget: 100000,
          costPerLeadActual: 98000,
          costPerLeadRate: 98,
          leadsTarget: 100,
          leadsActual: 115,
          leadsRate: 115,
          totalKpi: 106.85
        }
      ],

      unit: 'lượt',
      createdBy: 'Trần Thị Nguyên',
      createdDate: '19/06/2025',
      approvedBy: 'ADMIN',
      approvedDate: '19/06/2025',
      status: 'Đã duyệt'
    },
    {
      code: 'KPI03',
      evaluateMonth: '6',
      kpiGroup: 'KPI NV CSKH',
      department: 'CSKH',
      departmentData: [
        //SupportKPI
        //         Nguyễn Văn A
        // Trần Thị B
        // Lê Hoàng C
        // Phạm Mai D
        {
          key: '1',
          name: 'Nguyễn Văn A',
          code: 'NV0001',
          ticketTarget: 20,
          ticketResult: 18,
          ticketRate: 90,
          responseTarget: 100,
          responseResult: 90,
          responseRate: 90,
          csatTarget: 30,
          csatResult: 25,
          csatRate: 83,
          overdueTarget: '10',
          overdueResult: 8,
          overdueRate: 80,
          updateTarget: '95',
          updateResult: 90,
          updateRate: 94,
          totalKpi: 88.5
        },
        {
          key: '2',
          name: 'Trần Thị B',
          code: 'NV0002',
          ticketTarget: 20,
          ticketResult: 18,
          ticketRate: 90,
          responseTarget: 100,
          responseResult: 90,
          responseRate: 90,
          csatTarget: 30,
          csatResult: 25,
          csatRate: 83,
          overdueTarget: '10',
          overdueResult: 8,
          overdueRate: 80,
          updateTarget: '95',
          updateResult: 90,
          updateRate: 94,
          totalKpi: 88.5
        },
        {
          key: '3',
          name: 'Lê Hoàng C',
          code: 'NV0003',
          ticketTarget: 20,
          ticketResult: 18,
          ticketRate: 90,
          responseTarget: 100,
          responseResult: 90,
          responseRate: 90,
          csatTarget: 30,
          csatResult: 25,
          csatRate: 83,
          overdueTarget: '10',
          overdueResult: 8,
          overdueRate: 80,
          updateTarget: '95',
          updateResult: 90,
          updateRate: 94,
          totalKpi: 88.5
        },
        {
          key: '4',
          name: 'Phạm Mai D',
          code: 'NV0004',
          ticketTarget: 20,
          ticketResult: 18,
          ticketRate: 90,
          responseTarget: 100,
          responseResult: 90,
          responseRate: 90,
          csatTarget: 30,
          csatResult: 25,
          csatRate: 83,
          overdueTarget: '10',
          overdueResult: 8,
          overdueRate: 80,
          updateTarget: '95',
          updateResult: 90,
          updateRate: 94,
          totalKpi: 88.5
        }
      ],

      unit: 'báo giá',
      createdBy: 'Phạm Như Quỳnh',
      createdDate: '19/06/2025',
      approvedBy: 'ADMIN',
      approvedDate: '19/06/2025',
      status: 'Đã duyệt'
    }
  ])

  const handleFilter = (values: IKpiEvaluateFilter) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFakeData(fakeData)
  }

  const handleDelete = (record: IKpiEvaluate) => {
    setFakeData(fakeData.filter((item) => item.code !== record.code))
  }

  const columns: any[] = [
    { title: 'STT', key: 'stt', width: 60, align: 'center', render: (_, __, index) => index + 1 },
    { title: 'Mã KPI', dataIndex: 'code', key: 'code', align: 'center', width: 100 },
    {
      title: 'Tháng đánh giá',
      dataIndex: 'evaluateMonth',
      key: 'evaluateMonth',
      align: 'center',
      width: 120
    },
    {
      title: 'Bộ KPI áp dụng',
      dataIndex: 'kpiGroup',
      key: 'kpiGroup',
      align: 'center',
      width: 200
    },
    // { title: 'Đơn vị đo', dataIndex: 'unit', key: 'unit', align: 'center', width: 120 },
    {
      title: 'Người báo cáo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      align: 'center',
      width: 150
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      align: 'center',
      width: 150
    },
    {
      title: 'Ngày duyệt',
      dataIndex: 'approvedDate',
      key: 'approvedDate',
      align: 'center',
      width: 150
    },
    {
      title: 'Người duyệt',
      dataIndex: 'approvedBy',
      key: 'approvedBy',
      align: 'center',
      width: 150
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 120,
      render: (status: string) => (
        <Tag color={status === 'Đã duyệt' ? 'green' : 'red'}>{status}</Tag>
      )
    },
    {
      title: 'Tác vụ',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <>
          <DetailButton data={record}></DetailButton>
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]

  return (
    <BaseView>
      <FilterKPICategory onFilter={handleFilter} onReset={handleReset} isLoading={false} />
      <BaseTable columns={columns} data={fakeData} total={fakeData.length} isLoading={false} />
    </BaseView>
  )
}

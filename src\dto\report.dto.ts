export interface IReportDashbroad {
  totalCustomer: [TotalCustomer[], number]
  totalCustomerUsingProduct: [TotalCustomer[], number]
  totalLicense: [TotalLicense[], number]
  totalProduct: [TotalProduct[], number]
}

export interface TotalCustomer {
  id: string
  createdDate: string
  updatedDate: string
  createdBy: any
  updatedBy: any
  version: number
  name: string
  phone: string
  address: string
  provinceCode: any
  districtCode: any
  wardCode: any
  email: string
  password: any
  avatar: string
  taxCode: string
  note: any
  role: string
  status: string
  referrerId: any
}

export interface TotalLicense {
  id: string
  createdDate: string
  updatedDate: string
  createdBy: any
  updatedBy: any
  version: number
  key: string
  issuedAt: string
  expiredAt: string
  status: string
  notes: any
  maxUsers: string
  currentUsers: string
  customerId: string
  productId: string
}

export interface TotalProduct {
  id: string
  createdDate: string
  updatedDate: string
  createdBy: any
  updatedBy: any
  version: number
  groupId: any
  name: string
  title: string
  description: string
  defaultMaxUsers: string
  vat: string
  note: string
  status: string
  type: string
}

export interface IFilterRevenueReport {
  company?: string
  customerCode?: string
  creditLimitFrom?: number
  creditLimitTo?: number
  phone?: string
  pageIndex: number
  pageSize: number
}

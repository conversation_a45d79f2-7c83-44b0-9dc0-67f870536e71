import { useMutation, useQueryClient } from '@tanstack/react-query'
import { rootApiService, toastService } from '~/services/@common'
import { endpoint_catalog } from '~/services/endpoints'
import { useListCatalog } from './useListCatalog'

export interface UseUpdateCatalogParams {
  id: string
  name?: string
  itemCategory?: string
  itemGroup?: string
  description?: string
  images: string[]
  attachments: string[]
}

export const useUpdateCatalog = () => {
  const { refetch } = useListCatalog({})
  return useMutation({
    mutationFn: (body: UseUpdateCatalogParams) =>
      rootApiService.post(endpoint_catalog.update, body),
    onSuccess: () => {
      refetch()
      toastService.success('Cập nhật product thành công')
    },
    onError: (error) => {
      toastService.error('Cập nhật product thất bại')
    }
  })
}

import { SaveOutlined } from '@ant-design/icons'
import { Row, Col, Form, Input, InputNumber, Button, Select, Card } from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect } from 'react'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import { NSCatalog } from '~/common/enums/NSCatalog'
import { useCreateCatalog } from '~/hooks/catalog/useCreateCatalog'
import { toastService } from '~/services'

const { Option } = Select
const { TextArea } = Input

interface CreateItemModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateCatalogModal = ({ open, onClose, onSuccess }: CreateItemModalProps) => {
  const { mutateAsync: createProduct, isPending } = useCreateCatalog()
  const [form] = useForm()
  useEffect(() => {
    form.resetFields()
  }, [open])

  const handleCreate = async (values: any) => {
    if (!values) return
    createProduct(values).then(() => {
      onClose()
    })
  }
  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical' onFinish={handleCreate}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label='Tên sản phẩm'
                name='name'
                rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}>
                <Input placeholder='Nhập tên sản phẩm' />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item label='Loại sản phẩm' name='type'>
                <Select placeholder='Chọn loại sản phẩm'>
                  {Object.values(NSCatalog.EType).map((item) => (
                    <Select.Option key={item.code} value={item.code}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Nhóm sản phẩm' name='itemGroup'>
                <Select placeholder='Chọn nhóm sản phẩm' />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item
                label='Đơn vị tính'
                name='unit'
                rules={[{ required: true, message: 'Vui lòng chọn đơn vị tính' }]}>
                <Select placeholder='Chọn đơn vị tính'>
                  {Object.values(NSCatalog.EUnit).map((item) => (
                    <Select.Option key={item.code} value={item.code}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label='Đơn giá'
                name='unitPrice'
                rules={[{ required: true, message: 'Vui lòng nhập đơn giá' }]}>
                <InputNumber
                  min={0}
                  formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  style={{ width: '100%' }}
                  placeholder='Nhập đơn giá'
                />
              </Form.Item>
            </Col>

            {/* <Col span={8}>
              <Form.Item label='Hình ảnh sản phẩm' name='images'>
                <Input type='file' placeholder='Chọn hình ảnh sản phẩm' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Đính kèm' name='attachments'>
                <Input type='file' placeholder='Chọn file đính kèm' />
              </Form.Item>
            </Col> */}

            <Col span={24}>
              <Form.Item label='Mô tả' name='description'>
                <TextArea rows={4} placeholder='Nhập mô tả' />
              </Form.Item>
            </Col>
          </Row>

          <Row
            style={{
              textAlign: 'center',
              borderTop: '1px solid #f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              padding: '16px 0'
            }}>
            <Button onClick={onClose} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type='primary' icon={<SaveOutlined />} htmlType='submit'>
              Thêm mới
            </Button>
          </Row>
        </Form>
      </Card>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo sản phẩm mới'
      description='Thêm sản phẩm mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateCatalogModal

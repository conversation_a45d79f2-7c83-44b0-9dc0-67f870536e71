// Marketing-campaign components

import { DeleteOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { BaseButton } from '~/components'
import { IDepartment, IDepartmentFilter } from '~/dto/department.dto'
import { toastService } from '~/services'
import BaseView from '~/components/BaseView'
import { Row, Col } from 'antd'
import BaseTable from '~/components/BaseTable'
import FilterProduct from './components/FilterProduct'
import moment from 'moment'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'

export const DepartmentView = () => {
  const handleFilter = (values: IDepartmentFilter) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      id: null,
      name: '',
      description: '',
      status: '',
      createdAt: '',
      updatedAt: '',
      pageIndex: 0,
      pageSize: 10
    })
  }

  const [filter, setFilter] = useState<IDepartmentFilter>({
    id: null,
    name: '',
    description: '',
    status: '',
    createdAt: '',
    updatedAt: '',
    pageIndex: 0,
    pageSize: 10
  })

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const columns: any[] = [
    {
      title: 'Mã phòng ban',
      dataIndex: 'id',
      key: 'id',
      width: 90
    },
    {
      title: 'Tên phòng ban',
      dataIndex: 'name',
      key: 'name',
      width: 150
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 150
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 150
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]
  const handleDelete = async (item: IDepartment) => {
    try {
      // TODO: Implement delete functionality
      toastService.success('Xóa thành công')
    } catch (error) {
      toastService.handleError(error)
    }
  }

  const [fakeData, setFakeData] = useState<IDepartment[]>([
    {
      id: '1',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '2',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '3',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '4',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '5',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '6',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '7',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '8',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '9',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '10',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '11',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '12',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '13',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '14',
      name: 'Phòng kinh doanh',
      description: 'Phòng kinh doanh',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    }
  ])

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={false}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={fakeData}
            total={0}
            isLoading={false}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

import { useState, FC, useEffect } from 'react'
import { Badge, Button, Col, Row, Tooltip } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { ICustomer } from '~/dto/customer.dto'
import DetailButton from './component/DetailButton'
import EditButton from './component/EditButton'
import { BaseButton } from '~/components'
import { DeleteOutlined } from '@ant-design/icons'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { useListCustomer } from '~/hooks/customer/useListCustomer'
import { useCustomerFilterConfig } from './component/useCustomerFilterConfig'
import { ECustomer } from '~/common/enums/NSCustomer'

interface IFilterCustomer {
  pageIndex: number
  pageSize: number
}

type IProps = {}

const ListCustomerView: FC<IProps> = () => {
  const { t } = useTranslation()

  const { filterData, filterFields, handleFilter, handleFilterReset, setFilterData } =
    useCustomerFilterConfig()
  const [selectedStage, setSelectedStage] = useState<string | null>(null)
  const { data, isLoading, total } = useListCustomer(filterData)

  const handleClickFilter = (stage: string, data: any) => {
    if (stage === selectedStage) {
      setSelectedStage(null)
      delete filterData.customerType
      setFilterData({ ...filterData })
      return
    }
    setSelectedStage(stage)
    setFilterData({
      ...filterData,
      customerType: stage
    })
  }

  // const { data, isLoading, total } = useListCustomer()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {}

  const handleDelete = (record: ICustomer) => {}

  const columns: ColumnsType<ICustomer> = [
    {
      title: t('customer:customer.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer.columns.code'),
      dataIndex: 'code',
      key: 'code',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.name'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.phone'),
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.address'),
      dataIndex: 'address',
      key: 'address',
      width: 250,
      align: 'center'
    },
    // {
    //   title: t('customer:customer.columns.customerType'),
    //   dataIndex: 'customerType',
    //   key: 'customerType',
    //   width: 250,
    //   align: 'center'
    // },
    {
      title: t('customer:customer.columns.salesRep'),
      dataIndex: 'salesRep',
      key: 'salesRep',
      width: 250,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.department'),
      dataIndex: 'department',
      key: 'department',
      width: 250,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.ranking'),
      dataIndex: 'ranking',
      key: 'ranking',
      width: 280,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.source'),
      dataIndex: 'source',
      key: 'source',
      width: 250,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.industry'),
      dataIndex: 'industry',
      key: 'industry',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.region'),
      dataIndex: 'region',
      key: 'region',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.columns.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-')
    },
    {
      title: t('customer:customer.columns.action'),
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <EditButton data={value} />
            <DetailButton data={record} />
            <BaseButton
              danger
              type='primary'
              shape='circle'
              icon={<DeleteOutlined />}
              tooltip='Delete'
              onClick={() => handleDelete(record)}
            />
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <BaseFilter
        onReset={handleFilterReset}
        isLoading={false}
        filters={filterFields}
        onFilter={handleFilter}
      />
      <Row>
        {Object.values(ECustomer.ECustomerType).map(({ name, value, label, color }) => (
          <Tooltip key={value} title={name} color={color}>
            <Button
              onClick={() => handleClickFilter(value, null)}
              style={{
                margin: 10,
                height: 32,
                fontWeight: 'bold',
                backgroundColor: color,
                opacity: selectedStage === null || selectedStage === value ? 1 : 0.3,
                transition: 'opacity 0.3s'
              }}>
              {name}
            </Button>
          </Tooltip>
        ))}
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data}
            total={total}
            isLoading={false}
            onPageChange={handlePageChange}
            scroll={{ x: 2000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

export default ListCustomerView

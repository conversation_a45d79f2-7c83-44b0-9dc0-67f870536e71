import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  DatePicker
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useState } from 'react'
import { EProduct } from '~/common/enums/NSProduct'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker

interface CreateGiftModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateGiftModal = ({
  open,
  onClose,
  onSuccess
}: CreateGiftModalProps) => {
  const [form] = useForm()
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const { mutateAsync: createProduct, isPending } = useCreateProduct()

  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } =
    useUploadSingle()

  const handleSave = async (values: CreateProductReq) => {
    if (!values) return

    const body = {
      ...values,
      images: fileList.map((file) => file.url)
    }

    try {
      await createProduct(body)
      onClose()
      onSuccess?.()
      // Reset form and file list
      form.resetFields()
      setFileList([])
    } catch (error) {
      toastService.error('Tạo chương trình thất bại')
    }
  }

  // Handle image upload
  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!')
      return false
    }

    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      message.error('Hình ảnh phải nhỏ hơn 5MB!')
      return false
    }

    return true
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  const modalContent = (
    <Form form={form} layout='vertical' onFinish={handleSave}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Tên chương trình'
            name='name'
            rules={[
              { required: true, message: 'Vui lòng nhập tên chương trình' }
            ]}>
            <Input placeholder='Tên chương trình' />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Đối tượng áp dụng'
            name='targetAudience'
            rules={[
              { required: true, message: 'Vui lòng chọn đối tượng áp dụng' }
            ]}>
            <Select placeholder='Chọn đối tượng áp dụng'>
              <Option value='all'>Tất cả khách hàng</Option>
              <Option value='new'>Khách hàng mới</Option>
              <Option value='existing'>Khách hàng hiện tại</Option>
              <Option value='vip'>Khách hàng VIP</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Mức doanh số tích lũy'
            name='salesRange'
            rules={[
              { required: true, message: 'Vui lòng chọn khoảng doanh số' }
            ]}>
            <RangePicker
              style={{ width: '100%' }}
              format='DD/MM/YYYY'
              placeholder={['Từ ngày', 'Đến ngày']}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Hình thức quy đổi'
            name='conversionType'
            rules={[
              { required: true, message: 'Vui lòng chọn hình thức quy đổi' }
            ]}>
            <Select placeholder='Chọn hình thức quy đổi'>
              <Option value='points'>Điểm tích lũy</Option>
              <Option value='voucher'>Voucher</Option>
              <Option value='gift'>Quà tặng</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Giá trị quà tặng'
            name='giftValue'
            rules={[
              { required: true, message: 'Vui lòng nhập giá trị quà tặng' }
            ]}>
            <InputNumber
              style={{ width: '100%' }}
              placeholder='Giá trị quà tặng'
              min={0}
              addonAfter='đ'
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Hạng thành viên'
            name='memberLevel'
            rules={[
              { required: true, message: 'Vui lòng chọn hạng thành viên' }
            ]}>
            <Select placeholder='Chọn hạng thành viên'>
              <Option value='bronze'>Đồng</Option>
              <Option value='silver'>Bạc</Option>
              <Option value='gold'>Vàng</Option>
              <Option value='platinum'>Bạch kim</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Đơn vị'
            name='unit'
            rules={[{ required: true, message: 'Vui lòng nhập đơn vị' }]}>
            <Input placeholder='Đơn vị' />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Thời hạn'
            name='validityPeriod'
            rules={[{ required: true, message: 'Vui lòng chọn thời hạn' }]}>
            <RangePicker
              style={{ width: '100%' }}
              format='DD/MM/YYYY'
              placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label='Hình ảnh chương trình' name='images'>
        <Upload
          listType='picture-card'
          fileList={fileList}
          onChange={handleUploadChange}
          beforeUpload={beforeUpload}
          multiple
          accept='image/*'
          customRequest={async ({ file, onSuccess }) => {
            try {
              const formData = new FormData()
              formData.append('file', file as File)
              await uploadSingle(formData).then((res) => {
                handleUploadChange(res)
              })
            } catch (error) {}
          }}>
          {fileList.length >= 8 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item label='Ghi chú' name='note'>
            <TextArea rows={2} placeholder='Ghi chú' />
          </Form.Item>
        </Col>
      </Row>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16
        }}>
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button
          type='primary'
          htmlType='submit'
          icon={<SaveOutlined />}
          loading={isPending}>
          Tạo chương trình
        </Button>
      </div>
    </Form>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo chương trình mới'
      description='Thêm chương trình mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateGiftModal

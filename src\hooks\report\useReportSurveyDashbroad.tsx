import { useQuery } from '@tanstack/react-query'

export interface IReportSurveyFeedback {
  id: string
  surveyName: string
  trackingId: string
  customerName: string
  customerCode: string
  createdBy: string
  createdAt: string
  totalScore: number
  percentage: number
}

interface IReportSurveyFeedbackResponse {
  data: IReportSurveyFeedback[]
  total: number
}

const DUMMY_REPORT_SURVEY_FEEDBACK: IReportSurveyFeedbackResponse = {
  data: [
    {
      id: 'KS001',
      surveyName: 'Khảo sát hài lòng khách hàng',
      trackingId: 'TRK123456',
      customerName: 'Nguyễn Văn A',
      customerCode: 'CUST001',
      createdBy: 'Trần Thị B',
      createdAt: '2024-06-01T09:30:00Z',
      totalScore: 8.5,
      percentage: 85.0
    },
    {
      id: 'KS002',
      surveyName: '<PERSON>h<PERSON>o sát chất lượng dịch v<PERSON>',
      trackingId: 'TRK654321',
      customerName: 'L<PERSON> Thị <PERSON>',
      customerCode: 'CUST002',
      createdBy: '<PERSON><PERSON>ễ<PERSON>',
      createdAt: '2024-06-02T14:15:00Z',
      totalScore: 7.2,
      percentage: 72.0
    },
    {
      id: 'KS003',
      surveyName: 'Khảo sát trải nghiệm sản phẩm',
      trackingId: 'TRK789012',
      customerName: 'Phạm Văn E',
      customerCode: 'CUST003',
      createdBy: 'Lê Thị F',
      createdAt: '2024-06-03T10:45:00Z',
      totalScore: 5.8,
      percentage: 58.0
    },
    {
      id: 'KS004',
      surveyName: 'Khảo sát dịch vụ hậu mãi',
      trackingId: 'TRK345678',
      customerName: 'Ngô Thị G',
      customerCode: 'CUST004',
      createdBy: 'Phạm Văn H',
      createdAt: '2024-06-04T11:20:00Z',
      totalScore: 9.0,
      percentage: 90.0
    },
    {
      id: 'KS005',
      surveyName: 'Khảo sát hỗ trợ kỹ thuật',
      trackingId: 'TRK987654',
      customerName: 'Đỗ Văn I',
      customerCode: 'CUST005',
      createdBy: 'Nguyễn Thị J',
      createdAt: '2024-06-05T13:00:00Z',
      totalScore: 6.5,
      percentage: 65.0
    },
    {
      id: 'KS006',
      surveyName: 'Khảo sát tư vấn bán hàng',
      trackingId: 'TRK112233',
      customerName: 'Trần Thị K',
      customerCode: 'CUST006',
      createdBy: 'Lê Văn L',
      createdAt: '2024-06-06T15:30:00Z',
      totalScore: 8.0,
      percentage: 80.0
    },
    {
      id: 'KS007',
      surveyName: 'Khảo sát giao hàng',
      trackingId: 'TRK445566',
      customerName: 'Phạm Văn M',
      customerCode: 'CUST007',
      createdBy: 'Ngô Thị N',
      createdAt: '2024-06-07T09:10:00Z',
      totalScore: 7.8,
      percentage: 78.0
    },
    {
      id: 'KS008',
      surveyName: 'Khảo sát bảo hành',
      trackingId: 'TRK778899',
      customerName: 'Lê Thị O',
      customerCode: 'CUST008',
      createdBy: 'Đỗ Văn P',
      createdAt: '2024-06-08T16:45:00Z',
      totalScore: 6.0,
      percentage: 60.0
    },
    {
      id: 'KS009',
      surveyName: 'Khảo sát chăm sóc khách hàng',
      trackingId: 'TRK990011',
      customerName: 'Nguyễn Văn Q',
      customerCode: 'CUST009',
      createdBy: 'Trần Thị R',
      createdAt: '2024-06-09T10:25:00Z',
      totalScore: 9.5,
      percentage: 95.0
    },
    {
      id: 'KS010',
      surveyName: 'Khảo sát dịch vụ tổng thể',
      trackingId: 'TRK223344',
      customerName: 'Phạm Thị S',
      customerCode: 'CUST010',
      createdBy: 'Lê Văn T',
      createdAt: '2024-06-10T14:50:00Z',
      totalScore: 7.0,
      percentage: 70.0
    }
  ],
  total: 10
}

export const useReportSurveyDashbroad = () => {
  const { data = DUMMY_REPORT_SURVEY_FEEDBACK, isLoading } =
    useQuery<IReportSurveyFeedbackResponse>({
      queryKey: ['report-survey-feedback-dashboard'],
      queryFn: async () => {
        // Trả về dummy data, có thể thay thế bằng API thật sau này
        return DUMMY_REPORT_SURVEY_FEEDBACK
      },
      staleTime: 5 * 60 * 1000
    })

  return {
    data,
    isLoading
  }
}

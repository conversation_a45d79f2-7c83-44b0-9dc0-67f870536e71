import React, { useState } from 'react'
import { Card, Table, Tag, Button, Space, Input, Select, Collapse, Form, Row, Col, Checkbox } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import {
  EditOutlined,
  EyeOutlined,
  ReloadOutlined,
  SearchOutlined,
  SendOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { toastService } from '~/services'
import { getColorStatus } from '~/common/utils/common.utils'
import BaseTable from '~/components/BaseTable'

interface Contract {
  key: string
  contractCode: string
  contractNumber: string
  signedDate: string
  contractType: string
  contractName: string
  createdAt: string
  status: 'DRAFT' | 'SENT' | 'SIGNED'
  fromQuotation: boolean
}

const mockData: Contract[] = [
  {
    key: '1',
    contractCode: 'HD001',
    contractNumber: 'HD-2025-123456',
    signedDate: '2025-06-01',
    contractName: '<PERSON>ợ<PERSON> đồng dịch vụ A',
    contractType: '<PERSON>ị<PERSON> vụ',
    createdAt: '2025-06-01',
    status: 'DRAFT',
    fromQuotation: true
  },
  {
    key: '2',
    contractCode: 'HD002',
    contractNumber: 'HD-2025-789012',
    signedDate: '2025-06-05',
    contractName: 'Hợp đồng bảo trì B',
    contractType: 'Bảo trì',
    createdAt: '2025-06-05',
    status: 'SENT',
    fromQuotation: false
  },
  {
    key: '3',
    contractCode: 'HD003',
    contractNumber: 'HD-2025-345678',
    signedDate: '2025-06-10',
    contractName: 'Hợp đồng phân phối C',
    contractType: 'Phân phối',
    createdAt: '2025-06-10',
    status: 'SIGNED',
    fromQuotation: true
  },
  {
    key: '4',
    contractCode: 'HD004',
    contractNumber: 'HD-2025-901234',
    signedDate: '2025-06-11',
    contractName: 'Hợp đồng tư vấn D',
    contractType: 'Tư vấn',
    createdAt: '2025-06-11',
    status: 'DRAFT',
    fromQuotation: false
  },
  {
    key: '5',
    contractCode: 'HD005',
    contractNumber: 'HD-2025-567890',
    signedDate: '2025-06-12',
    contractName: 'Hợp đồng dịch vụ E',
    contractType: 'Dịch vụ',
    createdAt: '2025-06-12',
    status: 'SENT',
    fromQuotation: true
  },
  {
    key: '6',
    contractCode: 'HD006',
    contractNumber: 'HD-2025-123456',
    signedDate: '2025-06-13',
    contractName: 'Hợp đồng bảo trì F',
    contractType: 'Bảo trì',
    createdAt: '2025-06-13',
    status: 'SIGNED',
    fromQuotation: false
  },
  {
    key: '7',
    contractCode: 'HD007',
    contractNumber: 'HD-2025-789012',
    signedDate: '2025-06-14',
    contractName: 'Hợp đồng phân phối G',
    contractType: 'Phân phối',
    createdAt: '2025-06-14',
    status: 'DRAFT',
    fromQuotation: true
  },
  {
    key: '8',
    contractCode: 'HD008',
    contractNumber: 'HD-2025-345678',
    signedDate: '2025-06-15',
    contractName: 'Hợp đồng tư vấn H',
    contractType: 'Tư vấn',
    createdAt: '2025-06-15',
    status: 'SENT',
    fromQuotation: false
  },
  {
    key: '9',
    contractCode: 'HD009',
    contractNumber: 'HD-2025-901234',
    signedDate: '2025-06-16',
    contractName: 'Hợp đồng dịch vụ I',
    contractType: 'Dịch vụ',
    createdAt: '2025-06-16',
    status: 'SIGNED',
    fromQuotation: true
  },
  {
    key: '10',
    contractCode: 'HD010',
    contractNumber: 'HD-2025-567890',
    signedDate: '2025-06-17',
    contractName: 'Hợp đồng bảo trì J',
    contractType: 'Bảo trì',
    createdAt: '2025-06-17',
    status: 'DRAFT',
    fromQuotation: false
  }
]

export const ContractsComponent = () => {
  const [filters, setFilters] = useState({
    contractName: '',
    contractType: ''
  })
  const { t } = useTranslation()
  const navigate = useNavigate()

  const handleViewDetail = (id: string) => {
    navigate(`detail?id=${id}`)
  }

  const handleSendContract = (id: string) => {
    // TODO: Implement send contract functionality
    toastService.success('Gửi hợp đồng thành công')
  }

  const handleFilterChange = (field: string, value: string) => {
    setFilters({ ...filters, [field]: value })
  }

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Tag color='blue'>Nháp</Tag>
      case 'SENT':
        return <Tag color='green'>Đã gửi</Tag>
      case 'SIGNED':
        return <Tag color='orange'>Đã ký</Tag>
      default:
        return <Tag color='default'>Chờ xử lý</Tag>
    }
  }

  const validateHidden = (status: string) => {
    console.log(status)
    switch (status) {
      case 'DRAFT':
        return false
      case 'SENT':
        return false
      case 'SIGNED':
        return true
      default:
        return false
    }
  }

  const columns: ColumnsType<Contract> = [
    {
      title: 'Mã hợp đồng',
      dataIndex: 'contractCode',
      key: 'contractCode'
    },
    // Số ký hợp đồng
    {
      title: 'Số ký hợp đồng',
      dataIndex: 'contractNumber',
      key: 'contractNumber'
    },
    {
      title: 'Tên hợp đồng',
      dataIndex: 'contractName',
      key: 'contractName'
    },
    {
      title: 'Loại hợp đông',
      dataIndex: 'contractType',
      key: 'contractType'
    },
    // Ngày ký
    {
      title: 'Ngày ký',
      dataIndex: 'signedDate',
      key: 'signedDate'
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: 'Tạo từ báo giá',
      dataIndex: 'fromQuotation',
      key: 'fromQuotation',
      render: (value) => (<Checkbox checked={value} disabled />)
    },
    {
      title: 'Tác vụ',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type='primary'
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record.key)}>
            {t('button.title.view')}
          </Button>
          <Button icon={<EditOutlined />} disabled={validateHidden(record.status)}>
            {t('button.title.edit')}
          </Button>
          <Button icon={<SendOutlined />} onClick={() => handleSendContract(record.key)}>
            {t('button.title.send-customer')}
          </Button>
        </Space>
      )
    }
  ]

  return (
    <Card title='Danh sách hợp đồng'>
      <Collapse style={{ marginBottom: 16 }}>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label='Mã hợp đồng'>
                <Input
                  placeholder='Tìm theo hợp đồng'
                  onChange={(e) => handleFilterChange('contractName', e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Số ký hợp đồng'>
                <Input
                  placeholder='Tìm theo hợp đồng'
                  onChange={(e) => handleFilterChange('contractName', e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Tên hợp đồng'>
                <Input
                  placeholder='Tìm theo hợp đồng'
                  onChange={(e) => handleFilterChange('contractName', e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Loại hợp đồng'>
                <Select
                  placeholder='Loại hợp đồng'
                  onChange={(value) => handleFilterChange('contractType', value)}>
                  {['Dịch vụ', 'Bảo trì', 'Phân phối'].map((type) => (
                    <Select.Option key={type} value={type}>
                      {type}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space>
                <Button type='primary' icon={<SearchOutlined />}>
                  {t('button.title.search')}
                </Button>
                <Button type='default' icon={<ReloadOutlined />}>
                  Làm mới
                </Button>
              </Space>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      {/* Filter */}
      {/* <Space style={{ marginBottom: 16 }} wrap>
        <Input
          placeholder='Tìm theo hợp đồng'
          onChange={(e) => handleFilterChange('contractName', e.target.value)}
         
        />
        <Select
          placeholder='Loại hợp đồng'
          allowClear
          style={{ width: 160 }}
          onChange={(value) => handleFilterChange('contractType', value)}>
          {['Dịch vụ', 'Bảo trì', 'Phân phối'].map((type) => (
            <Select.Option key={type} value={type}>
              {type}
            </Select.Option>
          ))}
        </Select>
        <Button type='primary' icon={<SearchOutlined />}>
          {t('button.title.search')}
        </Button>
      </Space> */}
      <BaseTable
        columns={columns}
        data={mockData}
        pagination={{ pageSize: 5 }}
        total={mockData.length}
        isLoading={false}
      />
    </Card>
  )
}

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { rootApiService, toastService } from '~/services/@common'
import { endpoint_catalog } from '~/services/endpoints'
import { useListCustomer } from './useListCustomer'

interface useDeleteProductParams {
  id: string
}

export const useDeleteCatalog = () => {
  const { refetch } = useListCustomer({})
  return useMutation({
    mutationFn: (body: useDeleteProductParams) =>
      rootApiService.post(endpoint_catalog.inactive, body),
    onSuccess: () => {
      refetch()
      toastService.success('X<PERSON>a sản phẩm thành công')
    },
    onError: (error) => {
      toastService.error('Xóa sản phẩm thất bại')
    }
  })
}

export interface ICustomerContactResponse {
  data: ICustomerContact[]
  total: number
}

export interface ICustomerContact {
  id: string
  contactCode: string
  name: string
  customerName: string
  phone: string
  email: string
  branch: string
  note: string
  createdBy: string
  responsible: string
  createdAt: string
  isSpecialCare: boolean
  status: string,
  position: string,
}

export interface ICustomerContactFilter {
  pageIndex: number
  pageSize: number
  search?: string
  status?: string
  isSpecialCare?: boolean
  responsible?: string
}

export interface ICustomerContactResponse {
  data: ICustomerContact[]
  total: number
  pageIndex: number
  pageSize: number
}

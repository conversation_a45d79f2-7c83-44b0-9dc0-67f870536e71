import React from 'react'
import { Button, Popconfirm, Tooltip } from 'antd'
import type { ButtonType } from 'antd/es/button'

interface BaseButtonProps {
  icon?: React.ReactNode
  tooltip?: string
  type?: ButtonType
  danger?: boolean
  children?: React.ReactNode
  shape?: 'circle' | 'round'
  htmlType?: 'submit' | 'reset' | 'button'
  loading?: boolean
  confirmTitle?: string
  onConfirm: () => void
}

const BaseButton: React.FC<BaseButtonProps> = ({
  icon,
  tooltip = '',
  type = 'text',
  danger = false,
  children,
  loading,
  confirmTitle = '',
  onConfirm
}) => {
  const BtnComponent = (
    <Popconfirm title={confirmTitle} onConfirm={onConfirm}>
      <Button
        type={type}
        icon={icon}
        danger={danger}
        style={{ padding: '4px', marginRight: '4px' }}
        loading={loading}>
        {!icon && children}
      </Button>
    </Popconfirm>
  )

  return tooltip ? <Tooltip title={tooltip}>{BtnComponent}</Tooltip> : BtnComponent
}

export default BaseButton

import {
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  DatePicker,
  Space,
  message,
  Checkbox
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useState } from 'react'
import BaseText from '~/components/BaseText'
import { COLORS } from '~/common/constants'

const { Option } = Select
const { TextArea } = Input

const QUESTION_TYPES = [
  { label: 'Trắc nghiệm', value: 'multiple_choice' },
  { label: 'Hộp kiểm', value: 'checkbox' },
  { label: 'Trả lời ngắn', value: 'short_text' },
  { label: 'Trả lời đoạn', value: 'long_text' },
  { label: 'Đánh giá', value: 'rate' }
]

interface CreateSurveyFeedbackModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateSurveyFeedbackModal = ({
  open,
  onClose,
  onSuccess
}: CreateSurveyFeedbackModalProps) => {
  const [form] = useForm()
  const [fileList, setFileList] = useState([])

  const handleSave = (values: any) => {
    // TODO: call API
    console.log('===>', values)
  }

  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!')
      return false
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  const modalContent = (
    <Form form={form} layout='vertical' onFinish={handleSave}>
      {/* Thông tin khảo sát */}
      <BaseText variant='h4' weight='bold'>
        Thông tin khảo sát
      </BaseText>
      <Row gutter={24}>
        <Col span={18}>
          <Form.Item
            name='surveyName'
            label='Tên khảo sát'
            rules={[{ required: true, message: 'Bắt buộc' }]}>
            <Input placeholder='Nhập tên khảo sát' />
          </Form.Item>
          <Form.Item
            name='title'
            label='Tiêu đề'
            rules={[{ required: true, message: 'Bắt buộc' }]}>
            <Input placeholder='Nhập tiêu đề' />
          </Form.Item>
          <Form.Item
            name='description'
            label='Mô tả'
            rules={[{ required: true, message: 'Bắt buộc' }]}>
            <TextArea rows={2} placeholder='Nhập mô tả' />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item name='validDate' label='Valid Date'>
            <Input addonAfter='Ngày' type='number' min={1} defaultValue={5} />
          </Form.Item>
          <Form.Item
            label='Hình ảnh sản phẩm'
            name='images'
            style={{
              display: 'flex',
              justifyContent: 'center',
              flexDirection: 'column'
            }}>
            <Upload
              listType='picture-card'
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={beforeUpload}
              multiple
              accept='image/*'
              customRequest={async ({ file, onSuccess }) => {
                // Simulate upload success for demo
                // In real app, you would upload to your server here
                try {
                  const formData = new FormData()
                  formData.append('file', file as File)

                  // await uploadSingle(formData).then((res) => {
                  //   handleUploadChange(res)
                  // })
                } catch (error) {}
              }}>
              {fileList.length >= 8 ? null : uploadButton}
            </Upload>
          </Form.Item>
        </Col>
      </Row>
      {/* Danh sách phần */}
      <Form.List name='sections'>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, idx) => (
              <div
                key={field.key}
                style={{
                  border: '1px solid #e0e0e0',
                  borderRadius: 6,
                  marginBottom: 24,
                  padding: 16,
                  background: '#fafbfc'
                }}>
                <Row align='middle' justify='space-between'>
                  <Col>
                    <BaseText variant='h5' weight='bold'>
                      Phần {idx + 1}
                    </BaseText>
                  </Col>
                  <Col>
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => remove(field.name)}>
                      Xóa phần
                    </Button>
                  </Col>
                </Row>
                <Row gutter={16} style={{ marginTop: 8 }}>
                  <Col span={12}>
                    <Form.Item name={[field.name, 'title']} label='Tiêu đề'>
                      <Input placeholder='Nhập tiêu đề phần' />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={[field.name, 'description']} label='Mô tả'>
                      <TextArea rows={1} placeholder='Nhập mô tả phần' />
                    </Form.Item>
                  </Col>
                </Row>
                {/* Danh sách câu hỏi */}
                <Form.List name={[field.name, 'questions']}>
                  {(qFields, { add: addQ, remove: removeQ }) => (
                    <>
                      {qFields.map((qField, qIdx) => (
                        <div
                          key={qField.key}
                          style={{
                            border: '1px dashed #bfbfbf',
                            borderRadius: 4,
                            margin: '12px 0',
                            padding: 12,
                            background: '#fff'
                          }}>
                          <Row align='middle' justify='space-between'>
                            <Col>
                              <BaseText variant='body1' weight='bold'>
                                Câu hỏi {qIdx + 1}
                              </BaseText>
                            </Col>
                            <Col>
                              <Button
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => removeQ(qField.name)}>
                                Xóa câu hỏi
                              </Button>
                            </Col>
                          </Row>
                          <Row gutter={16} style={{ marginTop: 8 }}>
                            <Col span={12}>
                              <Form.Item
                                name={[qField.name, 'question']}
                                label='Câu hỏi'
                                rules={[
                                  { required: true, message: 'Bắt buộc' }
                                ]}>
                                <Input placeholder='Nhập câu hỏi' />
                              </Form.Item>
                            </Col>
                            <Col span={4}>
                              <Form.Item
                                name={[qField.name, 'score']}
                                label='Điểm'>
                                <InputNumber
                                  min={0}
                                  placeholder='Điểm'
                                  style={{ width: '100%' }}
                                />
                              </Form.Item>
                            </Col>
                            <Col span={8}>
                              <Form.Item
                                name={[qField.name, 'type']}
                                label='Loại câu hỏi'
                                rules={[
                                  { required: true, message: 'Bắt buộc' }
                                ]}>
                                <Select
                                  options={QUESTION_TYPES}
                                  placeholder='Chọn loại'
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row>
                            <Col>
                              <Form.Item
                                name={[qField.name, 'isRequired']}
                                valuePropName='checked'
                                initialValue={true}>
                                <Checkbox>Bắt buộc trả lời</Checkbox>
                              </Form.Item>
                            </Col>
                          </Row>
                        </div>
                      ))}
                      <Button
                        type='primary'
                        block
                        icon={<PlusOutlined />}
                        onClick={() => addQ()}>
                        Thêm câu hỏi
                      </Button>
                    </>
                  )}
                </Form.List>
              </div>
            ))}
            <Button
              type='default'
              style={{ backgroundColor: COLORS.PRIMARY, color: COLORS.WHITE }}
              block
              icon={<PlusOutlined />}
              onClick={() => add()}>
              Thêm phần
            </Button>
          </>
        )}
      </Form.List>
      {/* Phần kết thúc */}
      <div
        style={{
          border: '1px solid #e0e0e0',
          borderRadius: 6,
          marginTop: 18,
          marginBottom: 24,
          padding: 16,
          background: '#fafbfc'
        }}>
        <BaseText variant='h5' weight='bold'>
          Phần kết thúc
        </BaseText>
        <Form.Item
          name={['endSection', 'description']}
          label='Mô tả'
          rules={[{ required: true, message: 'Bắt buộc' }]}>
          <TextArea rows={2} placeholder='Vui lòng nhập...' />
        </Form.Item>
      </div>
      <Row justify='end'>
        <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
          Lưu khảo sát
        </Button>
      </Row>
    </Form>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo khảo sát mới'
      description='Thêm khảo sát mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateSurveyFeedbackModal

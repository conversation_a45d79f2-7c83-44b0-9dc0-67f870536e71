import { FC, useState } from 'react'
import { Card, Col, DatePicker, Form, Row, Button, Select } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { SearchOutlined } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface ICumulativeRevenue {
  id: string
  period: string
  lastYearRevenue: number
  currentRevenue: number
  growth: number
}

const fakeData: ICumulativeRevenue[] = [
  {
    id: '1',
    period: 'Q1 2024',
    lastYearRevenue: 450000000,
    currentRevenue: 500000000,
    growth: 11.11
  },
  {
    id: '2',
    period: 'Q2 2024',
    lastYearRevenue: 480000000,
    currentRevenue: 520000000,
    growth: 8.33
  },
  {
    id: '3',
    period: 'Q3 2024',
    lastYearRevenue: 500000000,
    currentRevenue: 550000000,
    growth: 10
  }
]

const CumulativeRevenue: FC = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const columns: ColumnsType<ICumulativeRevenue> = [
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.time'),
      dataIndex: 'period',
      key: 'period',
      width: 150
    },
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.revenue'),
      dataIndex: 'lastYearRevenue',
      key: 'lastYearRevenue',
      width: 200,
      render: (value: number) => value.toLocaleString('vi-VN') + ' VNĐ'
    },
    {
      title: t(
        'customer:customer.customer_detail.revenue_tab.columns.averageRevenue'
      ),
      dataIndex: 'currentRevenue',
      key: 'currentRevenue',
      width: 200,
      render: (value: number) => value.toLocaleString('vi-VN') + ' VNĐ'
    },
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.growth'),
      dataIndex: 'growth',
      key: 'growth',
      width: 150,
      render: (value: number) => (
        <span style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
          {value >= 0 ? '+' : ''}
          {value}%
        </span>
      )
    }
  ]

  const handleSearch = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()
      console.log('Search values:', values)
      // TODO: Implement API call
    } catch (error) {
      console.error('Validation failed:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <Card className='mb-4'>
        <Form
          form={form}
          layout='vertical'
          initialValues={{
            company: undefined,
            dateRange: undefined
          }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='company'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.company'
                )}
                rules={[{ required: true, message: 'Vui lòng chọn công ty' }]}>
                <Select
                  placeholder={t(
                    'customer:customer.customer_detail.revenue_tab.placeholders.selectCompany'
                  )}
                  options={[
                    { label: 'Công ty A', value: 'A' },
                    { label: 'Công ty B', value: 'B' }
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='dateRange'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.time'
                )}
                rules={[
                  {
                    required: true,
                    message: t(
                      'customer:customer.customer_detail.revenue_tab.form.time'
                    )
                  }
                ]}>
                <DatePicker.RangePicker
                  style={{ width: '100%' }}
                  picker='month'
                  format='MM/YYYY'
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} className='text-right'>
              <Button
                type='primary'
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}>
                {t(
                  'customer:customer.customer_detail.revenue_tab.form.viewRevenue'
                )}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>

      <Card>
        <BaseTable
          columns={columns}
          data={fakeData}
          total={fakeData.length}
          isLoading={loading}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  )
}

export default CumulativeRevenue

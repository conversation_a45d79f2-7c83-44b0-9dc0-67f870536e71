import { FC, useState } from 'react'
import { Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { FilterBySalesRepReport } from '../component/FilterBySalesRepReport'

interface ICustomerBySalesRep {
  id: string
  salesRep: string
  customerCode: string
  customerName: string
  phone: string
  email: string
  address: string
  industry: string
  mainProductGroup: string
  region: string
  province: string
  district: string
  ward: string
  market: string
  department: string
}

interface IFilterBySalesRepReport {
  salesRep?: string
  department?: string
  pageIndex: number
  pageSize: number
}

type IProps = {}

// Dữ liệu mẫu cho bảng
const dummyData: ICustomerBySalesRep[] = [
  {
    id: '1',
    salesRep: 'Nguyễn Văn A',
    customerCode: 'KH001',
    customerName: 'Công ty TNHH ABC',
    phone: '0901234567',
    email: '<EMAIL>',
    address: '123 Đường ABC, Quận 1, TP.HCM',
    industry: 'Công nghệ thông tin',
    mainProductGroup: '<PERSON><PERSON><PERSON> mềm quản lý',
    region: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 1',
    ward: 'Phường Bến Nghé',
    market: 'B2B',
    department: 'Kinh doanh'
  },
  {
    id: '2',
    salesRep: 'Trần Thị B',
    customerCode: 'KH002',
    customerName: 'Công ty CP XYZ',
    phone: '0912345678',
    email: '<EMAIL>',
    address: '456 Đường XYZ, Quận 2, TP.HCM',
    industry: 'Thương mại điện tử',
    mainProductGroup: 'Hệ thống thanh toán',
    region: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 2',
    ward: 'Phường Thảo Điền',
    market: 'B2C',
    department: 'Marketing'
  },
  {
    id: '3',
    salesRep: 'Lê Văn C',
    customerCode: 'KH003',
    customerName: 'Công ty TNHH DEF',
    phone: '0923456789',
    email: '<EMAIL>',
    address: '789 Đường DEF, Quận 3, TP.HCM',
    industry: 'Tài chính ngân hàng',
    mainProductGroup: 'Phần mềm kế toán',
    region: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 3',
    ward: 'Phường Võ Thị Sáu',
    market: 'B2B',
    department: 'Kinh doanh'
  },
  {
    id: '4',
    salesRep: 'Phạm Thị D',
    customerCode: 'KH004',
    customerName: 'Công ty CP GHI',
    phone: '0934567890',
    email: '<EMAIL>',
    address: '321 Đường GHI, Quận 4, TP.HCM',
    industry: 'Giáo dục',
    mainProductGroup: 'Hệ thống quản lý học tập',
    region: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 4',
    ward: 'Phường 15',
    market: 'B2B',
    department: 'Chăm sóc khách hàng'
  },
  {
    id: '5',
    salesRep: 'Hoàng Văn E',
    customerCode: 'KH005',
    customerName: 'Công ty TNHH JKL',
    phone: '0945678901',
    email: '<EMAIL>',
    address: '654 Đường JKL, Quận 5, TP.HCM',
    industry: 'Y tế',
    mainProductGroup: 'Hệ thống quản lý bệnh viện',
    region: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 5',
    ward: 'Phường 8',
    market: 'B2B',
    department: 'Phát triển kinh doanh'
  }
]

// Báo cáo khách hàng theo nhân viên kinh doanh
export const CustomerBySalesRepReport: FC<IProps> = (props: IProps) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [filteredData, setFilteredData] =
    useState<ICustomerBySalesRep[]>(dummyData)
  const [isLoading, setIsLoading] = useState(false)

  const columns: ColumnsType<ICustomerBySalesRep> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      fixed: 'left',
      render: (_, __, index) => (currentPage - 1) * pageSize + index + 1
    },
    {
      title: 'Nhân viên kinh doanh',
      dataIndex: 'salesRep',
      key: 'salesRep',
      width: 150,
      fixed: 'left'
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 130
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 180
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 250
    },
    {
      title: 'Ngành nghề',
      dataIndex: 'industry',
      key: 'industry',
      width: 150
    },
    {
      title: 'Nhóm sản phẩm chủ lực',
      dataIndex: 'mainProductGroup',
      key: 'mainProductGroup',
      width: 180
    },
    {
      title: 'Khu vực',
      dataIndex: 'region',
      key: 'region',
      width: 100
    },
    {
      title: 'Tỉnh/Thành',
      dataIndex: 'province',
      key: 'province',
      width: 120
    },
    {
      title: 'Quận/Huyện',
      dataIndex: 'district',
      key: 'district',
      width: 120
    },
    {
      title: 'Phường/Xã',
      dataIndex: 'ward',
      key: 'ward',
      width: 120
    },
    {
      title: 'Thị trường',
      dataIndex: 'market',
      key: 'market',
      width: 100
    },
    {
      title: 'Phòng ban',
      dataIndex: 'department',
      key: 'department',
      width: 120
    }
  ]

  const handlePageChange = (pageIndex: number, size: number) => {
    setCurrentPage(pageIndex)
    setPageSize(size)
  }

  const handleFilter = (values: IFilterBySalesRepReport) => {
    setIsLoading(true)

    // Simulate API call delay
    setTimeout(() => {
      let filtered = [...dummyData]

      // Filter by sales representative
      if (values.salesRep) {
        const salesRepMap: { [key: string]: string } = {
          sales_rep_1: 'Nguyễn Văn A',
          sales_rep_2: 'Trần Thị B',
          sales_rep_3: 'Lê Văn C',
          sales_rep_4: 'Phạm Thị D',
          sales_rep_5: 'Hoàng Văn E'
        }
        filtered = filtered.filter(
          (item) => item.salesRep === salesRepMap[values.salesRep!]
        )
      }

      // Filter by department
      if (values.department) {
        const departmentMap: { [key: string]: string } = {
          sales: 'Kinh doanh',
          marketing: 'Marketing',
          customer_service: 'Chăm sóc khách hàng',
          business_development: 'Phát triển kinh doanh',
          account_management: 'Quản lý khách hàng'
        }
        filtered = filtered.filter(
          (item) => item.department === departmentMap[values.department!]
        )
      }

      setFilteredData(filtered)
      setCurrentPage(1)
      setIsLoading(false)
    }, 500)
  }

  const handleReset = () => {
    setFilteredData(dummyData)
    setCurrentPage(1)
  }

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterBySalesRepReport
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={isLoading}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={filteredData}
            total={filteredData.length}
            isLoading={isLoading}
            scroll={{ x: 2500 }}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

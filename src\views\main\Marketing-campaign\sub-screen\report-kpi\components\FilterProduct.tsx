import { But<PERSON>, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { EProduct } from '~/common/enums/NSProduct'
import { IFilterCustomerContact } from '~/dto/complaint.dto'
import { IFilterReportKpiMarketingEmployee } from '~/dto/report-kpi-marketing-employee.dto'

interface IProps {
  onFilter: (values: IFilterReportKpiMarketingEmployee) => void
  onReset: () => void
  isLoading: boolean
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name='code' label='Mã nhân viên'>
                <Input placeholder='Nhập mã nhân viên' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='name' label='Tên nhân viên'>
                <Input placeholder='Nhập tên nhân viên' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='month' label='Tháng'>
                <DatePicker format={'MM/YYYY'} picker='month' style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='month' label='Tổng điểm KPI'>
                <Select placeholder='Chọn tổng điểm KPI'>
                  <Select.Option value=''>--Chọn tổng điểm KPI--</Select.Option>
                  <Select.Option value='1'>Từ 0% đến 50%</Select.Option>
                  <Select.Option value='2'>Từ 50% đến 100%</Select.Option>
                  <Select.Option value='3'>Trên 100%</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24} style={{ marginTop: 10 }}>
            <Form.Item style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  gap: 10,
                  justifyContent: 'center'
                }}>
                <Button
                  type='primary'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleFilter}
                  loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button
                  type='default'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleReset}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </div>
            </Form.Item>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterProduct

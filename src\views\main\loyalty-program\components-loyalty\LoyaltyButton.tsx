import { FC, useState, useEffect } from 'react'
import {
  Form,
  Select,
  Input,
  Space,
  Typography,
  Card,
  Row,
  Col,
  Tag,
  Statistic,
  Button
} from 'antd'
import { ILoyalty } from '~/dto/loyalty.dto'
import { IGift } from '~/dto/gift.dto'
import BaseButton from '~/components/BaseButton'
import { formatMoneyVND, formatDateCustom } from '~/common/helper/helper'
import BaseModal from '~/components/BaseModal'
import {
  GiftOutlined,
  CalendarOutlined,
  DollarOutlined,
  TeamOutlined
} from '@ant-design/icons'
import { useModal } from '~/hooks/useModal'
import { useGift } from '~/hooks/gift/useGift'

interface ILoyaltyButtonProps {
  record: ILoyalty
  onExchange: (giftId: string, quantity: number) => void
}

interface IGiftForm {
  giftId: string
  quantity: number
}

export const LoyaltyButton: FC<ILoyaltyButtonProps> = ({
  record,
  onExchange
}) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = Form.useForm<IGiftForm>()
  const { data: giftsData, isLoading } = useGift()
  const [selectedGift, setSelectedGift] = useState<IGift | null>(null)

  const handleGiftSelect = (giftId: string) => {
    const gift = giftsData?.data.find((g) => g.id === giftId)
    setSelectedGift(gift || null)
  }

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onExchange(values.giftId, values.quantity)
      closeModal()
      form.resetFields()
      setSelectedGift(null)
    })
  }

  const modalContent = (
    <div>
      <Card>
        <Form form={form} layout='vertical' initialValues={{ quantity: 1 }}>
          <Space direction='vertical' style={{ width: '100%' }}>
            <Typography.Text strong style={{ fontSize: '16px' }}>
              Số điểm tích lũy còn lại: {formatMoneyVND(record.remainingMoney)}
            </Typography.Text>

            <Form.Item
              name='giftId'
              label='Chọn quà tặng'
              rules={[{ required: true, message: 'Vui lòng chọn quà tặng' }]}>
              <Select
                placeholder='Chọn quà tặng'
                loading={isLoading}
                onChange={handleGiftSelect}
                options={giftsData?.data.map((gift) => ({
                  label: `${gift.description} - ${formatMoneyVND(
                    gift.giftValue
                  )}`,
                  value: gift.id
                }))}
              />
            </Form.Item>

            {selectedGift && (
              <Card size='small' style={{ marginBottom: '16px' }}>
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <Space direction='vertical' style={{ width: '100%' }}>
                      <div>
                        <Typography.Text type='secondary'>
                          Mô tả:
                        </Typography.Text>
                        <Typography.Text strong style={{ marginLeft: 8 }}>
                          {selectedGift.description}
                        </Typography.Text>
                      </div>
                      <div>
                        <Typography.Text type='secondary'>
                          Đối tượng:
                        </Typography.Text>
                        <Tag color='blue' style={{ marginLeft: 8 }}>
                          {selectedGift.targetAudience}
                        </Tag>
                      </div>
                      <div>
                        <Typography.Text type='secondary'>
                          Hạng thành viên:
                        </Typography.Text>
                        <Tag color='purple' style={{ marginLeft: 8 }}>
                          {selectedGift.memberLevel}
                        </Tag>
                      </div>
                    </Space>
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title='Giá trị quà tặng'
                      value={selectedGift.giftValue}
                      suffix={selectedGift.unit}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Space direction='vertical'>
                      <div>
                        <CalendarOutlined style={{ marginRight: 8 }} />
                        <Typography.Text type='secondary'>Từ: </Typography.Text>
                        <Typography.Text>
                          {formatDateCustom(
                            selectedGift.startDate,
                            'DD/MM/YYYY'
                          )}
                        </Typography.Text>
                      </div>
                      <div>
                        <CalendarOutlined style={{ marginRight: 8 }} />
                        <Typography.Text type='secondary'>
                          Đến:{' '}
                        </Typography.Text>
                        <Typography.Text>
                          {formatDateCustom(selectedGift.endDate, 'DD/MM/YYYY')}
                        </Typography.Text>
                      </div>
                    </Space>
                  </Col>
                </Row>
              </Card>
            )}

            <Form.Item
              name='quantity'
              label='Số lượng'
              rules={[
                { required: true, message: 'Vui lòng nhập số lượng' },
                { type: 'number', min: 1, message: 'Số lượng phải lớn hơn 0' }
              ]}>
              <Input type='number' min={1} />
            </Form.Item>
          </Space>
        </Form>
      </Card>
      <div style={{ marginTop: 24, textAlign: 'right' }}>
        <Space>
          <Button onClick={closeModal}>Hủy</Button>
          <Button type='primary' onClick={handleSubmit}>
            Xác nhận
          </Button>
        </Space>
      </div>
    </div>
  )

  return (
    <>
      <BaseButton icon={<GiftOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Đổi thưởng'
        description='Đổi thưởng'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

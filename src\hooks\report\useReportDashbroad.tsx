import { useQuery } from "@tanstack/react-query";
import { rootApiService } from "~/services/@common";
import { endpoints_report } from "~/services/endpoints";

export const useReportDashbroad = () => {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [endpoints_report.dashboard],
    queryFn: () => rootApiService.get(endpoints_report.dashboard),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });

  return {
    data,
    isLoading,
    refetch,
  };
};

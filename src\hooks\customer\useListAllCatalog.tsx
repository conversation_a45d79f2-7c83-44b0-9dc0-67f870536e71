import { useQuery } from '@tanstack/react-query'
import { rootApiService } from '~/services/@common'
import { endpoint_catalog } from '~/services/endpoints'

export const useListAllCatalog = () => {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [endpoint_catalog.list],
    queryFn: () => rootApiService.get(endpoint_catalog.list, { pageIndex: 1, pageSize: 99999 })
  })

  return {
    data,
    isLoading,
    refetch
  }
}

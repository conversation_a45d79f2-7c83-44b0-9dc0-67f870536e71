import {
  EyeOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CopyOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined,
} from "@ant-design/icons";
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  message,
  Image,
  Empty,
  Divider,
  Steps,
} from "antd";
import BaseButton from "~/components/BaseButton";
import BaseText from "~/components/BaseText";
import { useModal } from "../../../../hooks/useModal";
import BaseModal from "~/components/BaseModal";
import { formatDateCustom, formatMoneyVND } from "~/common/helper/helper";
import { IProduct } from "~/dto/product.dto";
import { EProduct, NSProduct } from "~/common/enums/NSProduct";
import { useDetailProduct } from "~/hooks/product/useDetailProduct";
import { ICustomerContact } from "~/dto/customer_contact.dto";
import { IMission } from "~/dto/missions.dto";
import { useEffect, useState } from "react";

const { Title, Paragraph, Text } = Typography;

interface DetailButtonProps {
  data: IMission;
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal();
  let [current, setCurrent] = useState(0);

  useEffect(() => {
    switch (data.status) {
      case 'NEW':
        setCurrent(0)
        break;
      case 'PROCESSING':
        setCurrent(1)
        break;
      case 'COMPLETED':
        setCurrent(2)
        break;
      default:
        setCurrent(0)
        break;
    }

    console.log(current);

  }, [open])




  const modalContent = (
    <div>
      {/* Product Overview Card */}
      <Card
        style={{ marginBottom: "16px" }}
        size="small"
      >
        <Steps current={current} labelPlacement="vertical" style={{ width: '100%' }} items={[
          {
            title: 'Mới tạo',
          },
          {
            title: 'Đang xử lý',
          },
          {
            title: 'Hoàn thành',
          },
        ]}>

        </Steps>
        <Row gutter={16}>
          {/* Trạng thái & phân loại */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text><br />
            <Text> {data.status} </Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Loại nhiệm vụ:</Text><br />
            <Text>{data.type}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tiêu đề:</Text><br />
            <Text>{data.title}</Text>
          </Col>



          {/* Phân công */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>NV theo dõi/giám sát:</Text><br />
            <Text>{data.supervisor}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phân công cho:</Text><br />
            <Text>{data.assignType}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>NV được phân công:</Text><br />
            <Text>{data.assignedEmployee}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phòng ban phân công:</Text><br />
            <Text>Tư vấn vật liệu - TT DVKH</Text>
          </Col>



          {/* Ngày tháng */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày dự kiến:</Text><br />
            <Text>{formatDateCustom(data.startDate, "DD/MM/YYYY")}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày thực tế:</Text><br />
            <Text>{formatDateCustom(data.endDate, "DD/MM/YYYY")}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Người tạo:</Text><br />
            <Text>TinDepTrai</Text>
          </Col>

        </Row>
        <Divider />
        <Row gutter={24}>
          <Col span={12}>
            <Row>
              <Col span={24} style={{ marginBottom: 16 }}>
                <Text strong>Mô tả chuyến thăm:</Text><br />
                <div style={{ background: '#f5f5f5', padding: '20px', borderRadius: '10px' }}>
                  <Text >
                    {data.description}
                  </Text>
                </div>
              </Col>

              {/* Ngày tiếp nhận */}
              <Col span={12} style={{ marginBottom: 16 }}>
                <Text strong>Ngày tiếp nhận:</Text><br />
                <Text>{formatDateCustom(data.checkInDate, "DD/MM/YYYY HH:mm")}</Text>
              </Col>

              {/* Ngày bắt đầu */}
              <Col span={12} style={{ marginBottom: 16 }}>
                <Text strong>Ngày bắt đầu:</Text><br />
                <Text>{formatDateCustom(data.startDate, "DD/MM/YYYY HH:mm")}</Text>
              </Col>

              {/* Ngày đến hạn */}
              <Col span={12} style={{ marginBottom: 16 }}>
                <Text strong>Ngày đến hạn:</Text><br />
                <Text>{formatDateCustom(data.dueDate, "DD/MM/YYYY HH:mm")}</Text>
              </Col>

              {/* Ngày kết thúc */}
              <Col span={12} style={{ marginBottom: 16 }}>
                <Text strong>Ngày kết thúc:</Text><br />
                <Text>{formatDateCustom(data.endDate, "DD/MM/YYYY HH:mm")}</Text>
              </Col>
            </Row>
          </Col>

          <Col span={12} style={{ borderLeft: '1px solid #e8e8e8', paddingLeft: 24 }}>
            {/* Khách hàng */}
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Khách hàng:</Text><br />
              <Text>{data.customerName}</Text>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>NV kinh doanh:</Text><br />
              <Text>Trần Tâm Phúc</Text>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Chi nhánh:</Text><br />
              <Text>DVKH - CN Đà Nẵng</Text>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Địa chỉ:</Text><br />
              <Text>{data.address}</Text>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Liên hệ:</Text><br />
              <Text>100350303 | Anh Thái</Text>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>SĐT liên hệ:</Text><br />
              <Text>0966540571</Text>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Email:</Text><br />
              <Text><EMAIL></Text>
            </Col>
          </Col>
        </Row>

      </Card >



    </div >
  );

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type="primary" />
      <BaseModal
        open={open}
        title="Chi tiết nhiệm vụ"
        description="Thông tin chi tiết nhiệm vụ"
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  );
};

export default DetailButton;

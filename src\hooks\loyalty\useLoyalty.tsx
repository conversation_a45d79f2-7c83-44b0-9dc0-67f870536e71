import { useQuery } from '@tanstack/react-query'
import { ILoyaltyResponse } from '~/dto/loyalty.dto'

const DUMMY_LOYALTY: ILoyaltyResponse = {
  data: [
    {
      id: '1',
      programName: 'Tích điểm đổi quà',
      customerCode: '1234567890',
      customerName: 'Nguyễn <PERSON>n <PERSON>',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-01-01',
      updatedAt: '2021-01-01'
    },
    {
      id: '2',
      programName: 'Ưu đãi sinh nhật',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-02-01',
      updatedAt: '2021-02-01'
    },
    {
      id: '3',
      programName: 'Ch<PERSON>o mừng thành viên mới',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-03-01',
      updatedAt: '2021-03-01'
    },
    {
      id: '4',
      programName: 'Tích lũy điểm',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-04-01',
      updatedAt: '2021-04-01'
    },
    {
      id: '5',
      programName: 'Hoàn tiền cuối năm',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-05-01',
      updatedAt: '2021-05-01'
    },
    {
      id: '6',
      programName: 'Ưu đãi doanh nghiệp',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-06-01',
      updatedAt: '2021-06-01'
    },
    {
      id: '7',
      programName: 'Tặng điểm ngày lễ',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-07-01',
      updatedAt: '2021-07-01'
    },
    {
      id: '8',
      programName: 'Giảm giá lần đầu',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-08-01',
      updatedAt: '2021-08-01'
    },
    {
      id: '9',
      programName: 'Tặng quà cuối năm',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-09-01',
      updatedAt: '2021-09-01'
    },
    {
      id: '10',
      programName: 'Tích điểm doanh nghiệp',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-10-01',
      updatedAt: '2021-10-01'
    },
    {
      id: '11',
      programName: 'Tặng voucher sinh nhật',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-11-01',
      updatedAt: '2021-11-01'
    },
    {
      id: '12',
      programName: 'Tặng điểm đăng ký',
      customerCode: '1234567890',
      customerName: 'Nguyễn Văn A',
      phoneNumber: '0909090909',
      programCode: '1234567890',
      totalAccumulatedMoney: 1000000,
      exchangedMoney: 100000,
      remainingMoney: 900000,
      status: 'ACTIVE',
      createdAt: '2021-12-01',
      updatedAt: '2021-12-01'
    }
  ],
  total: 12
}

export const useLoyalty = () => {
  //   const { data, isLoading, refetch } = useQuery({
  //     queryKey: ['loyalty'],
  //     queryFn: () => DUMMY_LOYALTY
  //   })

  return {
    data: DUMMY_LOYALTY,
    isLoading: false,
    refetch: () => {},
    total: DUMMY_LOYALTY.total
  }
}

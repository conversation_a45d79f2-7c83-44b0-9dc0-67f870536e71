import { useMutation, useQueryClient } from '@tanstack/react-query'
import { rootApiService, toastService } from '~/services/@common'
import { CreateProductReq } from '~/dto/product.dto'
import { endpoints_customer } from '~/services/endpoints'
import { useListCustomer } from './useListCustomer'

//createCatalog

export interface CreateCustomerReq {
  name?: string
  taxNumber?: string
  phone?: string
  email?: string
  fax?: string
  region?: string
  city?: string
  district?: string
  ward?: string
  address?: string
  source?: string
  market?: string
  salesRep?: string
  images?: string[]
}

export const useCreateCustomer = () => {
  const { refetch } = useListCustomer({})

  return useMutation({
    mutationFn: (body: CreateCustomerReq) => rootApiService.post(endpoints_customer.create, body),
    onSuccess: () => {
      refetch()
      toastService.success('Tạo mới khách hàng thành công')
    },
    onError: (error) => {
      toastService.error('Tạo khách hàng thất bại')
    }
  })
}

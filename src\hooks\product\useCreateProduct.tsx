import { useMutation, useQueryClient } from "@tanstack/react-query";
import { rootApiService, toastService } from "~/services/@common";
import { CreateProductReq } from "~/dto/product.dto";
import { endpoints_product } from "~/services/endpoints";

export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: CreateProductReq) =>
      rootApiService.post(endpoints_product.create, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_product.list, { pageIndex: 1, pageSize: 10 }],
      });
      toastService.success("Tạo product thành công");
    },
    onError: (error) => {
      toastService.error("Tạo product thất bại");
    },
  });
};

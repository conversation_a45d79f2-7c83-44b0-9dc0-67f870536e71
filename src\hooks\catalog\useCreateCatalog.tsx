import { useMutation, useQueryClient } from '@tanstack/react-query'
import { rootApiService, toastService } from '~/services/@common'
import { CreateProductReq } from '~/dto/product.dto'
import { endpoint_catalog } from '~/services/endpoints'
import { useListCatalog } from './useListCatalog'

//createCatalog

export interface CreateCatalogReq {
  name: string
  itemCategory: string
  itemGroup: string
  description: string
  images: string[]
  attachments: string[]
}

export const useCreateCatalog = () => {
  const { refetch } = useListCatalog({})

  return useMutation({
    mutationFn: (body: CreateCatalogReq) => rootApiService.post(endpoint_catalog.create, body),
    onSuccess: () => {
      refetch()
      toastService.success('Tạo product thành công')
    },
    onError: (error) => {
      toastService.error('Tạo product thất bại')
    }
  })
}

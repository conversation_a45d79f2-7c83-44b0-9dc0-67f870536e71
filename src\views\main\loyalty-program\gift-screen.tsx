import { useState, FC } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import { DeleteOutlined } from '@ant-design/icons'
import { toastService } from '~/services'
import BaseButton from '~/components/BaseButton'
import DetailButton from './components-gift/DetailButton'
import BaseTable from '~/components/BaseTable'
import EditButton from './components-gift/EditButton'
import { EProduct } from '~/common/enums/NSProduct'
import { useDeleteProduct } from '~/hooks/product/useDeleteProduct'
import { IGift } from '~/dto/gift.dto'
import FilterGift from './components-gift/FilterGift'
import { useGift } from '~/hooks/gift/useGift'

interface IFilterLoyalty {
  programName: string
  phoneNumber: string
  customerCode: string
  rewardExchange: string
  productName: string
  version: string
  status: string
  pageIndex: number
  pageSize: number
}

type IProps = {}

export const GiftScreen: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<IFilterLoyalty>({
    programName: '',
    phoneNumber: '',
    customerCode: '',
    rewardExchange: '',
    productName: '',
    version: '',
    status: '',
    pageIndex: 1,
    pageSize: 10
  })

  const { data, isLoading } = useGift()

  const { mutateAsync: deleteProduct } = useDeleteProduct()

  const handleFilter = (values: IFilterLoyalty) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      programName: '',
      phoneNumber: '',
      customerCode: '',
      rewardExchange: '',
      productName: '',
      version: '',
      status: '',
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = async (item: IGift) => {
    try {
      await deleteProduct({
        id: item.id
      })
    } catch (error) {
      toastService.handleError(error)
    }
  }

  const columns: ColumnsType<IGift> = [
    {
      title: t('loyalty:gift_screen.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('loyalty:gift_screen.columns.targetAudience'),
      dataIndex: 'targetAudience',
      key: 'targetAudience',
      width: 200,
      align: 'center'
    },
    {
      title: t('loyalty:gift_screen.columns.programName'),
      dataIndex: 'programName',
      key: 'programName',
      width: 200,
      align: 'center'
    },
    {
      title: t('loyalty:gift_screen.columns.memberLevel'),
      dataIndex: 'memberLevel',
      key: 'memberLevel',
      width: 150,
      align: 'center'
    },
    {
      title: t('loyalty:gift_screen.columns.conversionType'),
      dataIndex: 'conversionType',
      key: 'conversionType',
      width: 150,
      align: 'center'
    },
    {
      title: t('loyalty:gift_screen.columns.giftValue'),
      dataIndex: 'giftValue',
      key: 'giftValue',
      width: 150,
      align: 'center'
    },
    {
      title: t('loyalty:gift_screen.columns.unit'),
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      align: 'center'
    },
    {
      title: t('loyalty:gift_screen.columns.time'),
      children: [
        {
          title: t('loyalty:gift_screen.columns.startDate'),
          dataIndex: 'startDate',
          key: 'startDate',
          width: 150,
          align: 'center',
          render: (value: string) =>
            value ? new Date(value).toLocaleDateString('vi-VN') : '-'
        },
        {
          title: t('loyalty:gift_screen.columns.endDate'),
          dataIndex: 'endDate',
          key: 'endDate',
          width: 150,
          align: 'center',
          render: (value: string) =>
            value ? new Date(value).toLocaleDateString('vi-VN') : '-'
        }
      ]
    },
    {
      title: t('loyalty:gift_screen.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: string, record: IGift) => {
        return (
          <Tag
            color={EProduct.EProductStatus[record.status]?.color}
            style={{
              fontSize: '14px',
              padding: '4px 12px',
              width: '100%',
              textAlign: 'center'
            }}>
            {record.status?.toUpperCase() || ''}
          </Tag>
        )
      }
    },
    {
      title: t('loyalty:gift_screen.columns.action'),
      key: 'action',
      width: 150,
      align: 'center',
      fixed: 'right',
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <DetailButton data={value} />
            <EditButton data={value} />
            <BaseButton
              danger
              type='primary'
              shape='circle'
              icon={<DeleteOutlined />}
              tooltip='Delete'
              onClick={() => handleDelete(record)}
            />
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterGift
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={isLoading}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns as any}
            data={data?.data || []}
            total={data?.total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 2000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

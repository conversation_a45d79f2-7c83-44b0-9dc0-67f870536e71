import React, { useState } from 'react'
import { Table, Input, Select, Button, Space, Card, Tag, Row, Col, Form, Collapse } from 'antd'
import { EditOutlined, PlusOutlined, ReloadOutlined, SearchOutlined, StopFilled } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useNavigate } from 'react-router-dom'
import { getColorStatus } from '~/common/utils/common.utils'

const { Option } = Select

const mockData = [
  {
    key: '1',
    contentCode: '1225',
    contentTitle: '📦 Giao hàng nhanh nội thành trong 2 giờ',
    status: 'active',
    createdBy: 'linhtln',
    createdAt: '09/06/2025 08:30:08 AM'
  },
  {
    key: '2',
    contentCode: '1224',
    contentTitle: '🚛 Vận chuyển Bắc - Nam chỉ trong 48 giờ',
    status: 'active',
    createdBy: 'linhtln',
    createdAt: '12/05/2025 09:04:55 AM'
  },
  {
    key: '3',
    contentCode: '1223',
    contentTitle: '📦 Dịch vụ kho bãi và phân phối toàn quốc',
    status: 'inactive',
    createdBy: 'linhtln',
    createdAt: '09/05/2025 05:29:35 PM'
  },
  {
    key: '4',
    contentCode: '1222',
    contentTitle: '🎉 Khai trương trung tâm logistics Cần Thơ',
    status: 'active',
    createdBy: 'linhtln',
    createdAt: '05/05/2025 04:26:30 PM'
  },
  {
    key: '5',
    contentCode: '1221',
    contentTitle: '🚚 Giải pháp vận tải hàng nặng chuyên biệt',
    status: 'active',
    createdBy: 'nhanht',
    createdAt: '03/04/2025 03:18:44 PM'
  },
  {
    key: '6',
    contentCode: '1220',
    contentTitle: '📦 Dịch vụ lưu trữ & quản lý hàng hóa thông minh',
    status: 'inactive',
    createdBy: 'nhanht',
    createdAt: '05/03/2025 09:40:00 AM'
  },
  {
    key: '7',
    contentCode: '1219',
    contentTitle: '🌐 Giao nhận quốc tế - Nhanh chóng, an toàn',
    status: 'active',
    createdBy: 'nhanht',
    createdAt: '25/02/2025 02:38:16 PM'
  },
  {
    key: '8',
    contentCode: '1218',
    contentTitle: '🛳 Vận chuyển đường biển container nguyên chuyến',
    status: 'active',
    createdBy: 'nhanht',
    createdAt: '24/02/2025 11:11:17 AM'
  },
  {
    key: '9',
    contentCode: '1217',
    contentTitle: '✈️ Giao hàng nhanh qua đường hàng không',
    status: 'inactive',
    createdBy: 'nhanht',
    createdAt: '17/02/2025 03:02:11 PM'
  },
  {
    key: '10',
    contentCode: '1216',
    contentTitle: '🎄 Ưu đãi Giáng Sinh – Giảm 30% phí vận chuyển',
    status: 'active',
    createdBy: 'nhanht',
    createdAt: '20/12/2024 05:07:47 PM'
  }
]


export const ContentView = () => {
  const [searchCode, setSearchCode] = useState('')
  const [searchTitle, setSearchTitle] = useState('')
  const [status, setStatus] = useState('')
  const navigate = useNavigate()

  const handleEditContent = (id: string) => {
    navigate(`edit?id=${id}`)
  }

  const statusContent = (status: string) => {
    switch (status) {
      case 'active':
        return 'Đang hoạt động'
      case 'inactive':
        return 'Ngừng hoạt động'
      default:
        return 'active'
    }
  }

  const columns = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'index',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Mã nội dung',
      dataIndex: 'contentCode'
    },
    {
      title: 'Nội dung',
      dataIndex: 'contentTitle'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      render: (value) =>
        value === 'active' ? 
        <Tag color={getColorStatus(value)} >{statusContent(value).toUpperCase()}</Tag> : 
        <Tag color={getColorStatus(value)}>{statusContent(value).toUpperCase()}</Tag>
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy'
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt'
    },
    {
      title: 'Tác vụ',
      render: (record: any) => (
        <Space>
          <Button icon={<EditOutlined />} onClick={() => handleEditContent(record.id)}>
            Chỉnh sửa
          </Button>
          {
            record.status === 'active' ? (
              <Button icon={<StopFilled />} danger>
                Ngưng hoạt động
              </Button>
            ) : (
              <Button icon={<StopFilled />} type='primary'>
                Kích hoạt
              </Button>
            )
          }
        </Space>
      )
    }
  ]

  return (
    <Card title='Quản lý nội dung' style={{ width: '100%', height: '85vh', overflow: 'auto' }}>
      <Collapse style={{ marginBottom: 16 }}>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Mã nội dụng'>
                <Input
                  placeholder='Nhập mã nội dung...'
                  value={searchCode}
                  onChange={(e) => setSearchCode(e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Tiêu đề'>
                <Input
                  placeholder='Nhập tiêu đề...'
                  value={searchTitle}
                  onChange={(e) => setSearchTitle(e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Trạng thái'>
                <Select placeholder='-- All --' allowClear value={status} onChange={setStatus}>
                  <Option value=''>--Trạng thái--</Option>
                  <Option value='active'>Active</Option>
                  <Option value='inactive'>Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space style={{ alignItems: 'center', justifyContent: 'center' }}>
                <Button type='primary' icon={<SearchOutlined />}>
                  Tìm kiếm
                </Button>
                <Button icon={<ReloadOutlined />}>Làm mới</Button>
              </Space>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <BaseTable
        data={mockData}
        total={mockData.length}
        isLoading={false}
        columns={columns}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  )
}

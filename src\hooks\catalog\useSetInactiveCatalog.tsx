import { useMutation, useQueryClient } from '@tanstack/react-query'
import { rootApiService, toastService } from '~/services/@common'
import { CreateProductReq } from '~/dto/product.dto'
import { endpoint_catalog } from '~/services/endpoints'
import { useListCatalog } from './useListCatalog'
import { SetActiveCatalogReq } from './useSetActiveCatalog'

//createCatalog

export const useSetInactiveCatalog = () => {
  const { refetch } = useListCatalog({})

  return useMutation({
    mutationFn: (body: SetActiveCatalogReq) => rootApiService.post(endpoint_catalog.inactive, body),
    onSuccess: () => {
      refetch()
      toastService.success('Cập nhật trạng thái hoạt động thành công')
    },
    onError: (error) => {
      toastService.error('Cập nhật trạng thái hoạt động thất bại')
    }
  })
}

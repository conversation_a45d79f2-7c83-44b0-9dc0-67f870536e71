import React, { useState } from 'react'
import { Row, Col, Card } from 'antd'
import MissionTable from './components/MissionTable'
import { mockMissions, mockActivitySegments } from '~/common/constants'
import { ActivityChart } from '~/components'

export const MissionsView = () => {
  const [loading, setLoading] = useState(false)

  return (
    <div
      style={{
        padding: '24px',
        backgroundColor: '#f0f2f5',
        minHeight: '100vh'
      }}>
      <Row gutter={[24, 24]}>
        {/* Biểu đồ */}
        <Col xs={24} lg={24}>
          <Card title='Thống kê hoạt động theo segment' bordered={false}>
            <ActivityChart data={mockActivitySegments} />
          </Card>
        </Col>

        {/* Bảng Nhiệm vụ */}
        <Col xs={24} lg={24}>
          <MissionTable data={mockMissions} loading={loading} />
        </Col>
      </Row>
    </div>
  )
}

import React, { useState } from 'react'
import { Card, Table, Input, Select, Button, Space, Tag } from 'antd'
import {
  EyeOutlined,
  SendOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined
} from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ColumnsType } from 'antd/es/table'
import { AnyObject } from 'antd/es/_util/type'
import { toastService } from '~/services'
import { render } from '@testing-library/react'

const { Option } = Select

const mockData = [
  {
    key: 'BG-2025-057',
    customer: 'Công ty A',
    fromAddress: 'Hà Nội',
    toAddress: 'TP. Hồ Chí Minh',
    goodsType: 'Hàng khô',
    weight: 120,
    status: 'Đã gửi',
    transportMethod: 'Đường bộ',
    totalFee: 500000,
    deliveryTime: '2 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty B',
    fromAddress: 'Đà Nẵng',
    toAddress: '<PERSON><PERSON><PERSON>hơ',
    goodsType: 'Hàng lạnh',
    weight: 200,
    status: 'Chờ duyệt',
    transportMethod: 'Đường biển',
    totalFee: 750000,
    deliveryTime: '3 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty C',
    fromAddress: 'Hải Phòng',
    toAddress: 'Bình Dương',
    goodsType: 'Hàng khô',
    weight: 180,
    status: 'Đã gửi',
    transportMethod: 'Đường sắt',
    totalFee: 620000,
    deliveryTime: '2.5 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty D',
    fromAddress: 'Nghệ An',
    toAddress: 'Vũng Tàu',
    goodsType: 'Hàng lạnh',
    weight: 150,
    status: 'Đã gửi',
    transportMethod: 'Đường bộ',
    totalFee: 580000,
    deliveryTime: '3 ngày'
  },
  {
    key: '5BG-2025-057',
    customer: 'Công ty E',
    fromAddress: 'Huế',
    toAddress: 'Đà Lạt',
    goodsType: 'Hàng khô',
    weight: 90,
    status: 'Đã duyệt',
    transportMethod: 'Đường bộ',
    totalFee: 430000,
    deliveryTime: '2 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty F',
    fromAddress: 'Hà Tĩnh',
    toAddress: 'TP. Hồ Chí Minh',
    goodsType: 'Hàng lạnh',
    weight: 210,
    status: 'Đã gửi',
    transportMethod: 'Đường sắt',
    totalFee: 800000,
    deliveryTime: '4 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty G',
    fromAddress: 'Cần Thơ',
    toAddress: 'Hà Nội',
    goodsType: 'Hàng khô',
    weight: 130,
    status: 'Đã gửi',
    transportMethod: 'Đường hàng không',
    totalFee: 1500000,
    deliveryTime: '1 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty H',
    fromAddress: 'Quảng Ninh',
    toAddress: 'Long An',
    goodsType: 'Hàng lạnh',
    weight: 175,
    status: 'Hủy',
    transportMethod: 'Đường bộ',
    totalFee: 690000,
    deliveryTime: '2.5 ngày'
  },
  {
    key: 'BG-2025-057',
    customer: 'Công ty I',
    fromAddress: 'Hà Nam',
    toAddress: 'An Giang',
    goodsType: 'Hàng khô',
    weight: 160,
    status: 'Đã gửi',
    transportMethod: 'Đường biển',
    totalFee: 710000,
    deliveryTime: '3 ngày'
  },
  {
    key: '1BG-2025-057',
    customer: 'Công ty J',
    fromAddress: 'Thanh Hóa',
    toAddress: 'Bình Thuận',
    goodsType: 'Hàng lạnh',
    weight: 140,
    status: 'Nháp',
    transportMethod: 'Đường sắt',
    totalFee: 670000,
    deliveryTime: '3 ngày'
  }
]

while (mockData.length < 10) {
  const i = mockData.length + 1
  mockData.push({
    key: i.toString(),
    customer: `Công ty ${String.fromCharCode(64 + i)}`,
    fromAddress: 'Hải Phòng',
    toAddress: 'Đồng Nai',
    goodsType: 'Hàng khô',
    weight: 100 + i * 10,
    status: 'Đã gửi',
    transportMethod: i % 2 === 0 ? 'Đường bộ' : 'Đường sắt',
    totalFee: 400000 + i * 10000,
    deliveryTime: `${1 + (i % 3)} ngày`
  })
}

export const QuotationsComponent = () => {
  const navigate = useNavigate()
  const [filters, setFilters] = useState({
    customer: '',
    goodsType: '',
    transportMethod: ''
  })
  const { t } = useTranslation()

  const handleFilterChange = (field: string, value: string) => {
    setFilters({ ...filters, [field]: value })
  }

  const filteredData = mockData.filter((item) => {
    return (
      (!filters.customer || item.customer.includes(filters.customer)) &&
      (!filters.goodsType || item.goodsType === filters.goodsType) &&
      (!filters.transportMethod ||
        item.transportMethod === filters.transportMethod)
    )
  })

  const handleEditQuotation = (id: string) => {
    navigate(`edit?id=${id}`)
  }

  const handleDetailQuotation = (id: string) => {
    navigate(`detail?id=${id}`)
  }

  const handleSendQuotation = (id: string) => {
    // TODO: Implement send quotation functionality
    toastService.success('Gửi báo giá thành công')
  }

  const handleDeleteQuotation = (id: string) => {
    // TODO: Implement delete quotation functionality
    toastService.success('Xóa báo giá thành công')
  }

  const genertateStatusColor = (status: string) => {
    switch (status) {
      case 'Đã gửi':
        return 'green'
      case 'Chờ duyệt':
        return 'orange'
      case 'Đã duyệt':
        return 'blue'
      case 'Hủy':
        return 'red'
      default:
        return 'default'
    }
  }

  const columns = [
    {
      title: 'Số báo giá',
      dataIndex: 'key'
    },
    {
      title: t('quotation:quotation.header.customer'),
      dataIndex: 'customer'
    },
    {
      title: t('quotation:quotation.header.fromAddress'),
      dataIndex: 'fromAddress'
    },
    {
      title: t('quotation:quotation.header.toAddress'),
      dataIndex: 'toAddress'
    },
    {
      title: t('quotation:quotation.header.goodsType'),
      dataIndex: 'goodsType'
    },
    // {
    //   title: t('quotation:quotation.header.weight'),
    //   dataIndex: 'weight'
    // },
    {
      title: t('quotation:quotation.header.status'),
      dataIndex: 'status',
      render: (status: string) => (
        <Tag
          color={genertateStatusColor(status)}
          style={{ fontSize: '14px', padding: '4px 12px' }}
          >
          {status}
        </Tag>
      )
    },
    {
      title: t('quotation:quotation.header.transportMethod'),
      dataIndex: 'transportMethod'
    },
    {
      title: t('quotation:quotation.header.totalFee'),
      dataIndex: 'totalFee',
      render: (fee: number) => fee.toLocaleString()
    },
    {
      title: t('quotation:quotation.header.deliveryTime'),
      dataIndex: 'deliveryTime'
    }
  ]

  const actionColumn: ColumnsType<AnyObject> = [
    {
      title: t('quotation:quotation.header.action'),
      width: 250,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            onClick={() => handleDetailQuotation(record.id)}>
            {t('button.title.view')}
          </Button>
          <Button
            icon={<SendOutlined />}
            type='primary'
            onClick={() => handleSendQuotation(record.id)}>
            {t('quotation:quotation.send')}
          </Button>
          <Button
            icon={<EditOutlined />}
            type='default'
            onClick={() => handleEditQuotation(record.id)}>
            {t('button.title.edit')}
          </Button>
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDeleteQuotation(record.id)}>
            {t('button.title.delete')}
          </Button>
        </Space>
      )
    }
  ]

  return (
    <Card title={t('quotation:quotation.title')}>
      <Space style={{ marginBottom: 16 }} wrap>
        <Input
          placeholder={t('quotation:quotation.header.customer')}
          onChange={(e) => handleFilterChange('customer', e.target.value)}
          style={{ width: 200 }}
        />
        <Select
          placeholder={t('quotation:quotation.header.goodsType')}
          allowClear
          style={{ width: 160 }}
          onChange={(value) => handleFilterChange('goodsType', value)}>
          <Option value='Hàng khô'>Hàng khô</Option>
          <Option value='Hàng lạnh'>Hàng lạnh</Option>
        </Select>
        <Select
          placeholder={t('quotation:quotation.header.transportMethod')}
          allowClear
          style={{ width: 200 }}
          onChange={(value) => handleFilterChange('transportMethod', value)}>
          <Option value='Đường bộ'>Đường bộ</Option>
          <Option value='Đường biển'>Đường biển</Option>
          <Option value='Đường sắt'>Đường sắt</Option>
        </Select>
        <Button type='primary' icon={<SearchOutlined />}>
          {t('button.title.search')}
        </Button>
      </Space>
      <BaseTable
        columns={[...columns, ...actionColumn]}
        data={filteredData}
        total={filteredData.length}
        isLoading={false}
        scroll={{ x: 'max-content' }}></BaseTable>
    </Card>
  )
}

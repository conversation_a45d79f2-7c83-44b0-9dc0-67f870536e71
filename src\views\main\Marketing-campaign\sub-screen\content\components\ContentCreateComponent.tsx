import { SaveOutlined } from '@ant-design/icons'
import { Button, Col, Form, Input, Radio, Row, Select, Upload } from 'antd'
import ReactQuill from 'react-quill'
import BaseModal from '~/components/BaseModal'
interface CreateContentModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const { Option } = Select

export const ContentCreateComponent = ({
  open,
  onClose,
  onSuccess
}: CreateContentModalProps) => {
  const [form] = Form.useForm()
  const handleFinish = (values: any) => {
    console.log('Form values:', values)
  }

  const contentModal = (
    <div>
      <Form
        form={form}
        layout='vertical' 
        onFinish={handleFinish}
        initialValues={{ emailType: 'Có sẵn', status: 'active' }}>
        <Form.Item label='Mã nội dung' name='contentCode'>
          <Input disabled placeholder='1225' />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label='Tên nội dung'
              name='contentName'
              rules={[{ required: true }]}>
              <Input placeholder='' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Gửi từ email' name='fromEmail'>
              <Input placeholder='<EMAIL>' />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label='Loại Email' name='emailType'>
              <Radio.Group>
                <Radio value='Có sẵn'>Có sẵn</Radio>
                <Radio value='Cá nhân'>Cá nhân</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label='Tên người gửi' name='senderName'>
              <Select defaultValue='' placeholder='Chọn tên người gửi'>
                <Option value=''>--Chọn người gửi--</Option>
                <Option value='An'>An</Option>
                <Option value='Cường'>Cường</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Công ty' name='company'>
              <Select defaultValue='1000'>
                <Option value='1000'>1000 | Công Ty Cổ Phần Gỗ </Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Chi nhánh' name='branch'>
              <Select defaultValue='1000'>
                <Option value='1000'>1000 | Công Ty Cổ Phần Gỗ </Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label='Tiêu đề' name='subject' rules={[{ required: true }]}>
          <Input placeholder='Nhập tiêu đề' />
        </Form.Item>

        <Form.Item label='Trạng thái' name='status'>
          <Radio.Group>
            <Radio value='active'>Đang sử dụng</Radio>
            <Radio value='inactive'>Ngừng sử dụng</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item label='Tham số cho nội dung gửi' name='params'>
          <Input placeholder='##FullName##' disabled />
        </Form.Item>

        <Form.Item label='Upload hình ảnh'>
          <Upload beforeUpload={() => false} maxCount={1}>
            <Button>Chọn tệp</Button>
          </Upload>
        </Form.Item>

        <Form.Item label='Nội dung' name='content'>
          <ReactQuill theme='snow' style={{ height: 250 }} />
        </Form.Item>

        <Row>
          <Col span={24} style={{ textAlign: 'center' }}>
            <Form.Item>
              <Button
                type='primary'
                htmlType='submit'
                icon={<SaveOutlined />}
                style={{ marginTop: 24, alignItems: 'center' }}>
                Lưu
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  )
  return (
    <div>
      <BaseModal
        open={open}
        title='Tạo nội dung mới'
        description='Thêm nội dung mới vào hệ thống'
        onClose={onClose}
        childrenBody={contentModal}
      />
    </div>
  )
}

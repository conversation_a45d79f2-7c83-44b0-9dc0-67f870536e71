import { FC } from 'react'
import { Checkbox, Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface IContact {
  id: string
  code: string
  name: string
  position: string
  phoneNumber: string
  email: string
  isKeyDecision: boolean
  createdBy: string
  createdAt: string
}

type IProps = {}

const dummyData: IContact[] = [
  {
    id: '1',
    code: 'CT001',
    name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
    position: 'Gi<PERSON><PERSON> đốc',
    phoneNumber: '0987654321',
    email: 'nguy<PERSON><EMAIL>',
    isKeyDecision: true,
    createdBy: 'Admin',
    createdAt: '2024-03-20T08:00:00'
  },
  {
    id: '2',
    code: 'CT002',
    name: '<PERSON>r<PERSON><PERSON>h<PERSON>',
    position: '<PERSON>i<PERSON><PERSON> đốc',
    phoneNumber: '0987654322',
    email: '<EMAIL>',
    isKeyDecision: false,
    createdBy: 'Manager',
    createdAt: '2024-03-19T09:30:00'
  },
  {
    id: '3',
    code: 'CT003',
    name: 'Lê Văn C',
    position: 'Giám đốc',
    phoneNumber: '0987654323',
    email: '<EMAIL>',
    isKeyDecision: false,
    createdBy: 'Admin',
    createdAt: '2024-03-18T10:15:00'
  },
  {
    id: '4',
    code: 'CT004',
    name: 'Phạm Thị D',
    position: 'Trưởng phòng',
    phoneNumber: '0987654324',
    email: '<EMAIL>',
    isKeyDecision: false,
    createdBy: 'Manager',
    createdAt: '2024-03-17T14:20:00'
  },
  {
    id: '5',
    code: 'CT005',
    name: 'Hoàng Văn E',
    position: 'Kế toán trưởng',
    phoneNumber: '0987654325',
    email: '<EMAIL>',
    isKeyDecision: false,
    createdBy: 'Admin',
    createdAt: '2024-03-16T16:45:00'
  }
]

export const ContactTab: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  const columns: ColumnsType<IContact> = [
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.stt') || 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.code') || 'Mã',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.position') || 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.name') || 'Tên',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center'
    },
    {
      title:
        t('customer:customer.customer_detail.contact_tab.columns.phoneNumber') || 'Số điện thoại',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.email') || 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.createdBy') || 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer.customer_detail.contact_tab.columns.createdAt') || 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-')
    },
    {
      title:
        t('customer:customer.customer_detail.contact_tab.columns.isKeyDecision') ||
        'Người quyết định',
      dataIndex: 'isKeyDecision',
      key: 'isKeyDecision',
      width: 150,
      align: 'center',
      render: (value: boolean) => <Checkbox checked={value} disabled />
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={dummyData}
            total={dummyData.length}
            isLoading={false}
            scroll={{ x: 1200 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

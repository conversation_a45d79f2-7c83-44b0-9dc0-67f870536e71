import React from 'react'
import { Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip, Legend)

export const CampaignReportComponent = () => {
  const data = {
    labels: ['Total', 'Success', 'Fail', 'Opened'],
    datasets: [
      {
        label: 'Email Campaign Report',
        data: [
          44418,
          Math.floor(Math.random() * 40000),
          Math.floor(Math.random() * 500),
          Math.floor(Math.random() * 3000)
        ],
        backgroundColor: ['#ff6384', '#36a2eb', '#ffce56', '#4bc0c0'],
        borderWidth: 1
      }
    ]
  }

  const options = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: true }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  }

  return <Bar data={data} options={options} height={100} />
}

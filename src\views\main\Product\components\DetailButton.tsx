import {
  EyeOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CopyOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined,
} from "@ant-design/icons";
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  message,
  Image,
  Empty,
} from "antd";
import BaseButton from "~/components/BaseButton";
import BaseText from "~/components/BaseText";
import { useModal } from "../../../../hooks/useModal";
import BaseModal from "~/components/BaseModal";
import { formatDateCustom, formatMoneyVND } from "~/common/helper/helper";
import { IProduct } from "~/dto/product.dto";
import { EProduct, NSProduct } from "~/common/enums/NSProduct";
import { useDetailProduct } from "~/hooks/product/useDetailProduct";

const { Title, Paragraph } = Typography;

interface DetailButtonProps {
  data: IProduct;
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal();

  const { data: productDetail, isLoading } = useDetailProduct(data.id);

  if (!productDetail) return null;

  const {
    name,
    title,
    description,
    defaultMaxUsers,
    isRecommended,
    vat,
    note,
    status,
    type,
    customerUsed,
    createdDate,
    updatedDate,
    version,
    media,
  } = productDetail;

  const modalContent = (
    <div>
      {/* Product Overview Card */}
      <Card
        title={
          <Space>
            <AppstoreOutlined style={{ color: "#1890ff" }} />
            <BaseText>Chi tiết sản phẩm</BaseText>
          </Space>
        }
        style={{ marginBottom: "16px" }}
        size="small"
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={16}>
            <div>
              <Space align="start">
                <Title level={3} style={{ margin: 0 }}>
                  {name || "N/A"}
                </Title>
                {isRecommended && (
                  <Tag color="gold" icon={<StarOutlined />}>
                    Được khuyến nghị
                  </Tag>
                )}
              </Space>
              <BaseText
                color="textSecondary"
                size="lg"
                style={{ marginTop: "8px", display: "block" }}
              >
                {title || "Tiêu đề sản phẩm"}
              </BaseText>
              {description && (
                <Paragraph
                  ellipsis={{ rows: 2, expandable: true }}
                  style={{ marginTop: "12px", color: "#666" }}
                >
                  {description}
                </Paragraph>
              )}
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <Space direction="vertical" size="middle" style={{ width: "100%" }}>
              <div>
                <BaseText color="textSecondary">Trạng thái:</BaseText>
                <br />
                <Tag
                  color={EProduct.EProductStatus[status]?.color}
                  style={{ fontSize: "14px", padding: "4px 12px" }}
                >
                  {status?.toUpperCase() || "CHƯA XÁC ĐỊNH"}
                </Tag>
              </div>
              <div>
                <BaseText color="textSecondary">Loại sản phẩm:</BaseText>
                <br />
                <Tag
                  color={EProduct.EProductType[type]?.color}
                  style={{ fontSize: "14px", padding: "4px 12px" }}
                >
                  {type?.toUpperCase() || "STANDARD" || "CÁ NHÂN"}
                </Tag>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={[16, 16]}>
        {/* Usage Information */}
        <Col xs={24} lg={24}>
          <Card
            title={
              <Space>
                <TeamOutlined style={{ color: "#722ed1" }} />
                <BaseText>Thông tin sử dụng</BaseText>
              </Space>
            }
            size="small"
            style={{ height: "100%" }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <Statistic
                  title="Số lượng khách hàng sử dụng"
                  value={customerUsed || 0}
                  valueStyle={{ color: "#722ed1", fontSize: "24px" }}
                />
              </Col>
              <Col xs={24} sm={12}>
                <Statistic
                  title="Số lượng người dùng tối đa trên license"
                  value={defaultMaxUsers || "Không giới hạn"}
                  valueStyle={{ color: "#fa8c16", fontSize: "20px" }}
                />
              </Col>
            </Row>
            <Descriptions
              size="small"
              column={1}
              colon={false}
              style={{ marginTop: "16px" }}
            >
              <Descriptions.Item label="Version">
                <Tag color="blue">v{version || "1.0"}</Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* System Information */}
      <Card
        title={
          <Space>
            <CalendarOutlined style={{ color: "#fa8c16" }} />
            <BaseText>Thông tin hệ thống</BaseText>
          </Space>
        }
        style={{ marginTop: "16px" }}
        size="small"
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <div style={{ textAlign: "center" }}>
              <BaseText color="textSecondary">Ngày tạo</BaseText>
              <br />
              <BaseText weight="bold">
                {formatDateCustom(createdDate, "DD/MM/YYYY")}
              </BaseText>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div style={{ textAlign: "center" }}>
              <BaseText color="textSecondary">Ngày cập nhật</BaseText>
              <br />
              <BaseText weight="bold">
                {formatDateCustom(updatedDate, "DD/MM/YYYY")}
              </BaseText>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Media Gallery */}
      <Card
        title={
          <Space>
            <PictureOutlined style={{ color: "#eb2f96" }} />
            <BaseText>Hình ảnh sản phẩm</BaseText>
          </Space>
        }
        style={{ marginTop: "16px" }}
        size="small"
      >
        {media && media.length > 0 ? (
          <div>
            <BaseText
              color="textSecondary"
              style={{ marginBottom: "12px", display: "block" }}
            >
              {media.length} hình ảnh có sẵn
            </BaseText>
            <Row gutter={[12, 12]}>
              {media.map((mediaItem, index) => (
                <Col xs={12} sm={8} md={6} lg={4} key={mediaItem.id || index}>
                  <div
                    style={{
                      border: "1px solid #f0f0f0",
                      borderRadius: "8px",
                      overflow: "hidden",
                      aspectRatio: "1",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "#fafafa",
                    }}
                  >
                    <Image
                      src={mediaItem.imageUrl}
                      alt={`${name} - Image ${index + 1}`}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                      preview={{
                        mask: (
                          <div
                            style={{
                              background: "rgba(0,0,0,0.5)",
                              color: "white",
                              fontSize: "12px",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              height: "100%",
                            }}
                          >
                            <EyeOutlined style={{ marginRight: "4px" }} />
                            View
                          </div>
                        ),
                      }}
                      fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                    />
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <BaseText color="textSecondary">Không có hình ảnh</BaseText>
            }
            style={{ margin: "20px 0" }}
          />
        )}
      </Card>

      {/* Additional Information */}
      {note && (
        <Card
          title={
            <Space>
              <InfoCircleOutlined style={{ color: "#13c2c2" }} />
              <BaseText>Thông tin bổ sung</BaseText>
            </Space>
          }
          style={{ marginTop: "16px" }}
          size="small"
        >
          <div>
            <BaseText color="textSecondary">Ghi chú sản phẩm:</BaseText>
            <Paragraph style={{ marginTop: "8px", marginBottom: "0" }}>
              {note}
            </Paragraph>
          </div>
        </Card>
      )}
    </div>
  );

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type="primary" />
      <BaseModal
        open={open}
        title="Chi tiết sản phẩm"
        description="Thông tin chi tiết sản phẩm"
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  );
};

export default DetailButton;

import { configEnv } from '~/@config'

const baseUrl = configEnv().CONNECTORS.ROOT.baseUrl

export const endpoints_customer = {
  create: `${baseUrl}/api/client/customer/create`.trim(),
  list: `${baseUrl}/api/client/customer/list`.trim(),
  detail: `${baseUrl}/api/client/customer/detail`.trim(),
  delete: `${baseUrl}/api/client/customer/delete`.trim(),
  update: `${baseUrl}/api/client/customer/update`.trim(),
  listAll: `${baseUrl}/api/client/customer/list-all`.trim()
}

export const endpoints_product = {
  create: `${baseUrl}/api/admin/products/create`.trim(),
  list: `${baseUrl}/api/admin/products/list`.trim(),
  delete: `${baseUrl}/api/admin/products/delete`.trim(),
  update: `${baseUrl}/api/admin/products/update`.trim(),
  listAll: `${baseUrl}/api/admin/products/list-all`.trim(),
  detail: `${baseUrl}/api/admin/products/detail`.trim()
}

export const endpoints_provinces = {
  list: `${baseUrl}/api/publics/provinces-list`.trim(),
  listDistrict: `${baseUrl}/api/publics/districts-list`.trim(),
  listWard: `${baseUrl}/api/publics/wards-list`.trim(),
  fullAddress: `${baseUrl}/api/publics/full-address`.trim()
}

export const endpoints_report = {
  dashboard: `${baseUrl}/api/admin/reports/dashboard`.trim()
}

export const endpoints_upload = {
  uploadSingle: `${baseUrl}/api/admin/upload/upload_single_s3`.trim(),
  uploadMutiple: `${baseUrl}/api/admin/upload/upload_mutiple_s3`.trim()
}

export const endpoints_survey_feedback = {
  list: `${baseUrl}/survey-feedback`,
  getById: `${baseUrl}/survey-feedback`,
  create: `${baseUrl}/survey-feedback`,
  update: `${baseUrl}/survey-feedback`,
  delete: `${baseUrl}/survey-feedback`,
  uploadImage: `${baseUrl}/survey-feedback/upload`
}

export const endpoint_auth = {
  me: `${baseUrl}/api/client/auth/me`.trim()
}

export const endpoint_catalog = {
  create: `${baseUrl}/api/client/catalog/create`.trim(),
  list: `${baseUrl}/api/client/catalog/list`.trim(),
  inactive: `${baseUrl}/api/client/catalog/inactive`.trim(),
  active: `${baseUrl}/api/client/catalog/active`.trim(),
  update: `${baseUrl}/api/client/catalog/update`.trim(),
  detail: `${baseUrl}/api/client/catalog/detail`.trim()
}

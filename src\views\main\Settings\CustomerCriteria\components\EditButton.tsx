import { EditOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons'
import { Card, Row, Col, Form, Input, Button, Select, DatePicker } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'

import { useEffect } from 'react'
import { IDepartment } from '~/dto/department.dto'
import { useModal } from '~/hooks/useModal'
import { ICustomerCriteria } from '~/dto/customer-criteria.dto'

const { Option } = Select
const { TextArea } = Input

interface EditButtonProps {
  data: ICustomerCriteria
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()
  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data
      })
    }
  }, [open, data, form])

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='Mã' name='id'>
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Tên' name='name'>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Mô tả' name='description'>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Trạng thái' name='status'>
                <Select>
                  <Option value='Hoạt động'>Hoạt động</Option>
                  <Option value='Không hoạt động'>Không hoạt động</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <div
          style={{
            textAlign: 'center',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </div>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        tooltip='Chỉnh sửa'
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa tiêu chí khách hàng'
        description='Cập nhật tiêu khách đánh giá khách hàng'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

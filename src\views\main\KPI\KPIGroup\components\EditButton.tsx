import { DeleteOutlined, EditOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Radio,
  Card,
  Tag,
  Typography
} from 'antd'
import BaseButton from '~/components/BaseButton'
import { useModal } from '~/hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'

import moment from 'moment'
import { IKpiCategory, IKpiCategoryFilter, IKpiGroup } from '~/dto/Kpi.dto'
import BaseTable from '~/components/BaseTable'
import { ColumnsType } from 'antd/es/table'
import DetailButton from './DetailButton'
import { Table } from 'antd/lib'

const { Option } = Select
const { TextArea } = Input
const { Text } = Typography

interface EditButtonProps {
  data: IKpiGroup
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()

  useEffect(() => {
    form.setFieldsValue({
      ...data,
      createdDate: moment(data.createdDate).format('YYYY-MM-DD'),
      updatedDate: moment(data.updatedDate).format('YYYY-MM-DD')
    })
    // Set existing images to fileList
  }, [open, form])

  const [kpiCategoryData, setKpiCategoryData] = useState<any[]>()

  const frequency: any[] = [
    { label: 'Tháng', key: 'tháng' },
    { label: 'Quý', key: 'quý' },
    { label: 'Năm', key: 'năm' }
  ]

  const department: any[] = [
    { label: 'Kinh doanh', key: 'Kinh doanh' },
    { label: 'Marketing', key: 'Marketing' },
    { label: 'Chăm sóc khách hàng', key: 'CSKH' }
  ]

  const columns: ColumnsType<any> = [
    {
      title: 'Mã KPI',
      dataIndex: 'kpiCode',
      key: 'kpiCode',
      width: 50,
      align: 'center',
      render: (value: string, record: any, index: number) => (
        <Select
          value={value}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={handleSelectKpi(index)}>
          {allKpiCategoryData.map((item) => (
            <Option key={item.kpiCode} value={item.kpiCode}>
              {item.kpiCode}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Tên KPI',
      dataIndex: 'kpiName',
      key: 'kpiName',
      width: 200,
      align: 'center',
      render: (value: string, _: any, index: number) => (
        <Select
          value={value}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={handleSelectKpi(index)}>
          {allKpiCategoryData.map((item) => (
            <Option key={item.kpiCode} value={item.kpiName}>
              {item.kpiName}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Đơn vị đo',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      align: 'center',
      render: (value: string) => (
        <Select disabled value={value} style={{ width: '100%', textAlign: 'left' }}>
          {kpiCategoryData.map((item) => (
            <Option key={item.kpiCode} value={item.unit}>
              {item.unit}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Chỉ tiêu',
      dataIndex: 'target',
      key: 'target',
      width: 100,
      align: 'center',
      render: (_, record, index) => (
        <Input
          value={record.target}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={(e) => handleInputChange(e.target.value, index, 'target')}
        />
      )
    },
    {
      title: 'Trọng số (%)',
      dataIndex: 'weight',
      key: 'weight',
      width: 100,
      align: 'center',
      render: (_, record, index) => (
        <Input
          value={record.weight}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={(e) => handleInputChange(e.target.value, index, 'weight')}
        />
      )
    }
  ]

  const handleInputChange = (value: any, index: number, key: string) => {
    setKpiCategoryData(
      kpiCategoryData?.map((item, dataIndex: number) => {
        if (dataIndex === index) {
          item[key] = value
        }
        return item
      })
    )
  }

  const handleAddNewKpi = () => {
    setKpiCategoryData([
      ...kpiCategoryData,
      { kpiCode: '', kpiName: '', unit: '', target: 0, weight: 0 }
    ])
  }

  useEffect(() => {
    setKpiCategoryData(allKpiCategoryData.filter((item) => item.weight <= 10))
  }, [open])

  const allKpiCategoryData = [
    { kpiCode: 'KPI01', kpiName: 'Số khách hàng mới', unit: 'khách hàng', target: 10, weight: 30 },
    { kpiCode: 'KPI02', kpiName: 'Số cuộc gọi / gặp khách', unit: 'lượt', target: 20, weight: 10 },
    { kpiCode: 'KPI03', kpiName: 'Số báo giá gửi đi', unit: 'báo giá', target: 20, weight: 10 },
    { kpiCode: 'KPI04', kpiName: 'Doanh số ký hợp đồng', unit: 'VND', target: 3, weight: 30 },
    { kpiCode: 'KPI05', kpiName: 'Tỷ lệ chốt đơn', unit: '%', target: 30, weight: 10 },
    { kpiCode: 'KPI06', kpiName: 'Tỷ lệ cập nhật CRM', unit: '%', target: 100, weight: 10 }
  ]

  const handleSelectKpi = (index: number) => (value: string) => {
    const selectedKpi = allKpiCategoryData.find(
      (item) => item.kpiCode === value || item.kpiName === value
    )
    if (selectedKpi) {
      const newData = [...kpiCategoryData]
      newData[index] = {
        kpiCode: selectedKpi.kpiCode,
        kpiName: selectedKpi.kpiName,
        unit: selectedKpi.unit,
        target: 0,
        weight: 0
      }
      setKpiCategoryData(newData)
    }
  }

  const modalContent = (
    <div>
      <Form form={form} layout='vertical'>
        <Card title='Thông tin chung'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Tên bộ KPI' name='groupName'>
                <Input placeholder='Nhập tên bộ KPI' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Tần suất đo' name='frequency'>
                <Select allowClear placeholder='Chọn tần suất đo'>
                  {frequency.map((item) => (
                    <Select.Option key={item.key}>{item.label}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Phòng ban' name='department'>
                <Select allowClear placeholder='Chọn phòng ban'>
                  {department.map((item) => (
                    <Select.Option key={item.key}>{item.label}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <Card
          title={
            <Row>
              <span style={{ fontSize: 16, marginRight: 16 }}>Thiết lập chỉ tiêu</span>
              <span>
                <Button onClick={handleAddNewKpi} type='primary' icon={<PlusOutlined />} />
              </span>
            </Row>
          }>
          <Table dataSource={kpiCategoryData} columns={columns} pagination={false} />
        </Card>
      </Form>
      <Row>
        <Col span={24} style={{ textAlign: 'center' }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </Col>
      </Row>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type='primary' tooltip='Chỉnh sửa' />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa Bộ KPI'
        description='Cập nhật Bộ KPI'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

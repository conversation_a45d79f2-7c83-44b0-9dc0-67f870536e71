export interface ILoyaltyHistoryResponse {
  data: ILoyaltyHistory[]
  total: number
}

export interface ILoyaltyHistory {
  // Fixed columns
  rewardCode: string // Mã đổi thưởng
  exchangeDate: string // Ngày đổi thưởng
  customerCode: string // Mã khách hàng

  // Other columns
  customerName: string // Tên khách hàng
  phoneNumber: string // Số điện thoại
  programCode: string // Mã chương trình
  programName: string // Tên chương trình
  memberRank: string // Hạng thành viên
  totalAccumulatedMoney: number // Tổng DS tích luỹ
  atp: number // ATP
  remainingAtp: number // Còn lại/ATP
  conversionType: string // Hình thức quy đổi
  giftValue: number // Giá trị quà tặng
  unit: string // Đơn vị
  usedAmount: number // Sử dụng
}

export interface IFilterLoyaltyHistory {
  customerCode: string
  customerName: string
  phoneNumber: string
  programCode: string
  programName: string
  memberRank: string
  pageIndex: number
  pageSize: number
}

export enum ELoyaltyMemberRank {
  BRONZE = 'BRONZE',
  SILVER = 'SILVER',
  GOLD = 'GOLD',
  PLATINUM = 'PLATINUM'
}

export const LoyaltyMemberRankConfig = {
  [ELoyaltyMemberRank.BRONZE]: {
    label: 'Đồng',
    color: '#cd7f32'
  },
  [ELoyaltyMemberRank.SILVER]: {
    label: 'Bạc',
    color: '#c0c0c0'
  },
  [ELoyaltyMemberRank.GOLD]: {
    label: 'Vàng',
    color: '#ffd700'
  },
  [ELoyaltyMemberRank.PLATINUM]: {
    label: 'Bạch kim',
    color: '#e5e4e2'
  }
}

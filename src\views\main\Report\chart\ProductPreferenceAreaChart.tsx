import React from 'react'
import { Line } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface ProductPreferenceAreaChartProps {
  data?: {
    labels: string[]
    datasets: {
      label: string
      data: number[]
      borderColor: string
      backgroundColor: string
      fill: boolean
    }[]
  }
}

const ProductPreferenceAreaChart: React.FC<ProductPreferenceAreaChartProps> = ({ data }) => {
  // Dữ liệu mẫu - có thể thay thế bằng data từ props
  const defaultData = {
    labels: [
      'Vận chuyển nội địa',
      '<PERSON><PERSON><PERSON> hàng <PERSON> (Express)',
      '<PERSON>yển phát quốc tế',
      'Vận chuyển đường bộ',
      '<PERSON>ận chuyển đường biển',
      '<PERSON><PERSON><PERSON> hàng thu tiền hộ (COD)',
      '<PERSON><PERSON><PERSON> vụ kho bãi',
      '<PERSON>ịch vụ đóng gói hàng hóa',
      '<PERSON>ận tải hàng nặng',
      '<PERSON><PERSON><PERSON> vụ thủ tục hải quan',
      'Chuyển phát tiết kiệm',
      'Bốc xếp hàng hóa',
      'Cho thuê xe tải',
      'Vận chuyển container',
      'Giao hàng trong ngày',
      'Chuyển phát hỏa tốc',
      'Quản lý hàng tồn kho',
      'Dịch vụ hậu cần chuỗi cung ứng',
      'Dịch vụ giao nhận trọn gói',
      'Khai báo hải quan điện tử',
      'Vận chuyển đa phương thức',
      'Giải pháp logistics tích hợp'
    ],
    datasets: [
      {
        label: 'Thị hiếu sản phẩm',
        data: [
          85, 92, 78, 88, 95, 82, 90, 87, 93, 79, 86, 91, 84, 89, 83, 94, 80, 85, 92, 88, 90, 87
        ],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        fill: true
      }
    ]
  }

  const chartData = data || defaultData

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'Thị hiếu sản phẩm',
        font: {
          size: 16,
          weight: 'bold' as const
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${context.parsed.y}%`
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Tỷ lệ thị hiếu (%)',
          font: {
            size: 12,
            weight: 'bold' as const
          }
        },
        ticks: {
          stepSize: 10
        }
      },
      x: {
        title: {
          display: true,
          text: 'Sản phẩm',
          font: {
            size: 12,
            weight: 'bold' as const
          }
        },
        ticks: {
          maxRotation: 45,
          minRotation: 0,
          maxTicksLimit: 10
        }
      }
    }
  }

  return (
    <div
      style={{
        width: '100%',
        height: '450px',
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
      <Line data={chartData} options={options} />
    </div>
  )
}

export default ProductPreferenceAreaChart

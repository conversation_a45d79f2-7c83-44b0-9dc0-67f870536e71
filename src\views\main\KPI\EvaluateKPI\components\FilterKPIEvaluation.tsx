import { But<PERSON>, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd'
import { FC, useCallback, useState } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { EProduct } from '~/common/enums/NSProduct'
import { IFilterCustomerContact } from '~/dto/complaint.dto'
import { IKpiCategoryFilter, IKpiEvaluateFilter, IKpiGroup } from '~/dto/Kpi.dto'

interface IProps {
  onFilter: (values: IKpiEvaluateFilter) => void
  onReset: () => void
  isLoading: boolean
}

const FilterKPICategory: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  const groupKpiData = [
    {
      code: 'KPI01',
      groupName: 'KPI NV KD',
      department: 'Kinh doanh',
      createdBy: 'ADMIN',
      createdDate: '19/06/2025',
      updatedDate: '19/06/2025',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      code: 'KPI02',
      groupName: 'KPI NV MKT',
      department: 'Marketing',
      createdBy: 'ADMIN',
      createdDate: '19/06/2025',
      updatedDate: '19/06/2025',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      code: 'KPI03',
      groupName: 'KPI NV CSKH',
      department: 'CSKH',
      createdBy: 'ADMIN',
      createdDate: '19/06/2025',
      updatedDate: '19/06/2025',
      frequency: 'tháng',
      status: 'Hoạt động'
    }
  ]

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='createdDate' label='Ngày tạo'>
                <DatePicker placeholder='Chọn ngày tạo' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name='unit' label='Trạng thái'>
                <Select placeholder='Chọn trạng thái'>
                  <Select.Option value='Chờ duyệt'>Chờ duyệt</Select.Option>
                  <Select.Option value='Đã duyệt'>Đã duyệt</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name='createdBy' label='Tháng đánh giá'>
                <DatePicker format={'MM/YYYY'} picker='month' style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name='kpiGroup' label='Bộ KPI'>
                <Select placeholder='Chọn bộ KPI' allowClear style={{ width: '100%' }}>
                  {groupKpiData.map((item) => (
                    <Select.Option key={item.code}>{item.groupName}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name='approvedBy' label='Ngày tạo'>
                <DatePicker placeholder='Chọn ngày tạo' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name='approvedDate' label='Ngày duyệt'>
                <DatePicker placeholder='Chọn ngày duyệt' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24} style={{ marginTop: 10 }}>
            <Form.Item style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  gap: 10,
                  justifyContent: 'center'
                }}>
                <Button
                  type='primary'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleFilter}
                  loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button
                  type='default'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleReset}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </div>
            </Form.Item>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterKPICategory

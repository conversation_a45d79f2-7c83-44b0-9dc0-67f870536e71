import { useQuery } from "@tanstack/react-query";
import { rootApiService } from "~/services/@common";
import { IProductDetail } from "~/dto/product.dto";
import { endpoint_catalog } from "~/services/endpoints";

export const useDetailCatalog = (id: string) => {
  const { data, isLoading, refetch } = useQuery<IProductDetail>({
    queryKey: [endpoint_catalog.detail, id],
    queryFn: () => rootApiService.post(endpoint_catalog.detail, { id }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });

  return {
    data,
    isLoading,
    refetch,
  };
};

import { EditOutlined, SaveOutlined } from '@ant-design/icons'
import { Row, Col, Form, Input, Button, Select, DatePicker, Radio } from 'antd'
import BaseButton from '~/components/BaseButton'
import { useModal } from '~/hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'

import moment from 'moment'
import { IKpiCategoryFilter } from '~/dto/Kpi.dto'

const { Option } = Select
const { TextArea } = Input

interface EditButtonProps {
  data: IKpiCategoryFilter
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()

  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        ...data
      })
      // Set existing images to fileList
    }
  }, [open, form])

  const unit: any[] = [
    { label: 'Kh<PERSON>ch hàng', key: 'kh<PERSON>ch hàng' },
    { label: 'L<PERSON>ợ<PERSON>', key: 'l<PERSON>ợ<PERSON>' },
    { label: 'Báo giá', key: 'báo giá' },
    { label: 'VNĐ', key: 'VNĐ' },
    { label: '%', key: '%' },
    { label: 'Cuộc gọi', key: 'cuộc' },
    { label: 'Lead', key: 'Lead' }
  ]

  const frequency: any[] = [
    { label: 'Tháng', key: 'tháng' },
    { label: 'Quý', key: 'quý' },
    { label: 'Năm', key: 'năm' }
  ]

  const department: any[] = [
    { label: 'Kinh doanh', key: 'Kinh doanh' },
    { label: 'Marketing', key: 'Marketing' },
    { label: 'Chăm sóc khách hàng', key: 'CSKH' }
  ]

  const modalContent = (
    <div>
      <Form form={form} layout='vertical'>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label='Mã KPI' name='kpiCode'>
              <Input placeholder='Nhập mã KPI' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Tên KPI' name='kpiName'>
              <Input placeholder='Nhập tên KPI' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Phòng ban' name='department'>
              <Select allowClear placeholder='Chọn phòng ban'>
                {department.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Đơn vị đo' name='unit'>
              <Select allowClear placeholder='Chọn đơn vị đo'>
                {unit.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Tần suất đo' name='frequency'>
              <Select allowClear placeholder='Chọn tần suất đo'>
                {frequency.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label='Mô tả' name='description'>
              <TextArea rows={4} placeholder='Nhập mô tả' />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Row>
        <Col span={24} style={{ textAlign: 'center' }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </Col>
      </Row>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type='primary' tooltip='Chỉnh sửa' />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa danh mục KPI'
        description='Cập nhật danh mục KPI'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

import { ICustomerContactResponse } from '~/dto/customer-contact.dto'

const DUMMY_DATA: ICustomerContactResponse = {
  data: [
    {
      id: '1',
      contactCode: 'LH001',
      name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
      customerName: 'Công ty TNHH ABC',
      phone: '0123456789',
      email: 'nguy<PERSON><PERSON>@example.com',
      branch: '<PERSON> <PERSON>h<PERSON><PERSON>',
      position: '<PERSON>i<PERSON><PERSON> đốc',
      note: '<PERSON><PERSON> chú',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-20T08:00:00Z',
      isSpecialCare: true,
      status: 'Hoạt động'
    },
    {
      id: '2',
      contactCode: 'LH002',
      name: '<PERSON><PERSON><PERSON><PERSON>h<PERSON>',
      position: '<PERSON><PERSON> giám đốc',
      customerName: 'Công ty TNHH XYZ',
      phone: '0987654321',
      email: '<EMAIL>',
      branch: '<PERSON> <PERSON>h<PERSON>',
      note: '<PERSON><PERSON> giám đốc',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-19T08:00:00Z',
      isSpecialCare: false,
      status: 'Hoạt động'
    },
    {
      id: '3',
      contactCode: 'LH003',
      name: 'Lê Văn C',
      customerName: 'Công ty TNHH DEF',
      phone: '0369852147',
      email: '<EMAIL>',
      branch: 'Chi nhánh Đà Nẵng',
      note: 'Ghi chú',
      createdBy: 'Admin',
      responsible: 'Nguyễn Văn ABC',
      createdAt: '2024-03-18T08:00:00Z',
      isSpecialCare: true,
      status: 'Không hoạt động',
      position: 'Giám đốc'
    },
    {
      id: '4',
      contactCode: 'LH004',
      name: 'Phạm Thị D',
      customerName: 'Công ty TNHH GHI',
      phone: '0852147963',
      email: '<EMAIL>',
      branch: 'Chi nhánh Cần Thơ',
      note: 'Giám đốc',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-17T08:00:00Z',
      isSpecialCare: false,
      status: 'Hoạt động',
      position: 'Giám đốc'
    },
    {
      id: '5',
      contactCode: 'LH005',
      name: 'Hoàng Văn E',
      customerName: 'Công ty TNHH JKL',
      phone: '0741852963',
      email: '<EMAIL>',
      branch: 'Chi nhánh Hải Phòng',
      note: 'Kế toán trưởng',
      createdBy: 'Admin',
      responsible: 'Trần Văn Cường',
      createdAt: '2024-03-16T08:00:00Z',
      isSpecialCare: true,
      status: 'Hoạt động',
      position: 'Kế toán trưởng'
    },
    {
      id: '6',
      contactCode: 'LH006',
      name: 'Vũ Thị F',
      customerName: 'Công ty TNHH MNO',
      phone: '0963258741',
      email: '<EMAIL>',
      branch: 'Chi nhánh Biên Hòa',
      note: 'Trưởng phòng nhân sự',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-15T08:00:00Z',
      isSpecialCare: false,
      status: 'Không hoạt động',
      position: 'Trưởng phòng nhân sự'
    },
    {
      id: '7',
      contactCode: 'LH007',
      name: 'Đỗ Văn G',
      customerName: 'Công ty TNHH PQR',
      phone: '0852369741',
      email: '<EMAIL>',
      branch: 'Chi nhánh Nha Trang',
      note: 'Phó giám đốc kinh doanh',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-14T08:00:00Z',
      isSpecialCare: true,
      status: 'Hoạt động',
      position: 'Phó giám đốc kinh doanh'
    },
    {
      id: '8',
      contactCode: 'LH008',
      name: 'Ngô Thị H',
      customerName: 'Công ty TNHH STU',
      phone: '0963258741',
      email: '<EMAIL>',
      branch: 'Chi nhánh Vũng Tàu',
      note: 'Trưởng phòng kỹ thuật',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-13T08:00:00Z',
      isSpecialCare: false,
      status: 'Hoạt động',
      position: 'Trưởng phòng kỹ thuật'
    },
    {
      id: '9',
      contactCode: 'LH009',
      name: 'Trịnh Văn I',
      customerName: 'Công ty TNHH VWX',
      phone: '0852369741',
      email: '<EMAIL>',
      branch: 'Chi nhánh Huế',
      note: 'Giám đốc tài chính',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-12T08:00:00Z',
      isSpecialCare: true,
      status: 'Không hoạt động',
      position: 'Giám đốc tài chính'
    },
    {
      id: '10',
      contactCode: 'LH010',
      name: 'Lý Thị J',
      customerName: 'Công ty TNHH YZ',
      phone: '0963258741',
      email: '<EMAIL>',
      branch: 'Chi nhánh Quy Nhơn',
      note: 'Trưởng phòng marketing',
      createdBy: 'Admin',
      responsible: 'Admin',
      createdAt: '2024-03-11T08:00:00Z',
      isSpecialCare: false,
      status: 'Hoạt động',
      position: 'Trưởng phòng marketing'
    }
  ],
  total: 10,
  pageIndex: 1,
  pageSize: 10
}

export const useListCustomerContact = () => {
  return {
    data: DUMMY_DATA,
    total: 10,
    isLoading: false,
    isError: false
  }
}

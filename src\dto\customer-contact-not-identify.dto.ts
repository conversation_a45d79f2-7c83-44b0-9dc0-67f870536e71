export interface ICustomerNotIdentifyResponse {
  data: ICustomerContactNotIdentify[]
  total: number
}

export interface ICustomerContactNotIdentify {
  id: string
  source: string
  companyCode: string
  companyName: string
  region: string
  address: string
  city: string
  district: string
  ward: string
  name: string
  email: string
  phone: string
  position: string
  firstAccessTime: string
  accessCount30Days: number
}

export interface ICustomerContactNotIdentifyFilter {
  pageIndex: number
  pageSize: number
  search?: string
}

export interface ICustomerContactNotIdentifyResponse {
  data: ICustomerContactNotIdentify[]
  total: number
}

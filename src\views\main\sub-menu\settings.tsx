import {
  AppstoreOutlined,
  AuditOutlined,
  FileDoneOutlined,
  Line<PERSON><PERSON>Outlined,
  SecurityScanOutlined,
  SolutionOutlined,
  TeamOutlined,
  UnorderedListOutlined,
  UserOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { PermissionsView } from '../Settings/Permissions'
import { CustomerCriteriaView } from '../Settings/CustomerCriteria'
import { DepartmentView } from '../Settings/Department'
import { EmployeeView } from '../Settings/Employee'
import { InvoiceView } from '../Settings/Invoice'
import { ContractView } from '../Settings/Contract'
import { QuotationView } from '../Settings/Quotation'
import { QuotationTemplateDetail } from '../Settings/Quotation/components/QuotationTemplateDetail'
import { QuotationTemplateEdit } from '../Settings/Quotation/components/QuotationTemplateEdit'
import { CatalogView } from '../Settings/Catalog'
import { CatalogCategoryView } from '../Settings/ItemCategory'
import { CatalogGroupView } from '../Settings/ItemGroup'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuSettings = [
  {
    path: 'employee',
    title: 'Nhân viên',
    view: <EmployeeView />,
    icon: <UserOutlined />
  },
  {
    path: 'department',
    title: 'Phòng ban',
    view: <DepartmentView />,
    icon: <TeamOutlined />
  },
  {
    path: 'permissions',
    title: 'Phân quyền',
    view: <PermissionsView />,
    icon: <SecurityScanOutlined />
  },
  //template báo giá

  {
    path: 'customer-criteria',
    title: 'Tiêu chí đánh giá khách hàng',
    view: <CustomerCriteriaView />,
    icon: <SolutionOutlined />
  },

  {
    path: 'quotation-template',
    title: 'Cấu hình Báo giá',
    view: <QuotationView />,
    icon: <AppstoreOutlined />
  },
  {
    path: 'quotation-template/detail',
    title: 'Chi tiết báo giá',
    view: <QuotationTemplateDetail />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'quotation-template/edit',
    title: 'Chỉnh sửa báo giá',
    view: <QuotationTemplateEdit />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  //template hợp đồng
  {
    path: 'contract',
    title: 'Cấu hình Hợp đồng',
    view: <ContractView />,
    icon: <FileDoneOutlined />
  },

  //template hóa đơn
  {
    path: 'invoice',
    title: 'Cấu hình Hóa đơn',
    view: <InvoiceView />,
    icon: <AuditOutlined />
  },

  // {
  //   path: 'catalog-category',
  //   title: 'Loại sản phẩm',
  //   view: <CatalogCategoryView />,
  //   icon: <AuditOutlined />
  // },

  // {
  //   path: 'catalog-group',
  //   title: 'Nhóm sản phẩm',
  //   view: <CatalogGroupView />,
  //   icon: <AuditOutlined />
  // },

  {
    path: 'catalog',
    title: 'Sản phẩm',
    view: <CatalogView />,
    icon: <AuditOutlined />
  }
  // Thiết lập KPI nhân viên
  // {
  //   path: 'kpi-employee',
  //   title: 'Thiết lập KPI nhân viên',
  //   view: <EmployeeSubScreenView />,
  //   icon: <LineChartOutlined />
  // }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu))

export default subMenuSettings

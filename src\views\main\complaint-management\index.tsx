import { useState, FC } from 'react'
import { Col, Row, Tag, Checkbox } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import { DeleteOutlined } from '@ant-design/icons'
import { toastService } from '~/services'
import BaseButton from '~/components/BaseButton'
import DetailButton from './components/DetailButton'
import BaseTable from '~/components/BaseTable'
import EditButton from './components/EditButton'
import FilterProduct from './components/FilterProduct'
import { useListComplaint } from '~/hooks/complaint/useListComplaint'
import { IComplaint, IFilterComplaint } from '~/dto/complaint.dto'

type IProps = {}

export const ComplaintManagementView: FC<IProps> = (props: IProps) => {
  const [filter, setFilter] = useState<IFilterComplaint>({
    title: '',
    customerCode: '',
    sapCode: '',
    supervisorId: '',
    assignedStaffId: '',
    startDate: '',
    endDate: '',
    pageIndex: 1,
    pageSize: 10
  })

  const { data, total, isLoading } = useListComplaint({})

  const [selectedRows, setSelectedRows] = useState<string[]>([])

  const handleFilter = (values: IFilterComplaint) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      title: '',
      customerCode: '',
      sapCode: '',
      supervisorId: '',
      assignedStaffId: '',
      startDate: '',
      endDate: '',
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = async (item: IComplaint) => {
    try {
      // TODO: Implement delete functionality
      toastService.success('Xóa thành công')
    } catch (error) {
      toastService.handleError(error)
    }
  }

  const columns: ColumnsType<IComplaint> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Chọn',
      dataIndex: 'select',
      key: 'select',
      width: 100,
      render: (_, record) => (
        <Checkbox
          checked={selectedRows.includes(record.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedRows([...selectedRows, record.id])
            } else {
              setSelectedRows(selectedRows.filter((id) => id !== record.id))
            }
          }}
        />
      )
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
      align: 'center'
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      align: 'center'
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      align: 'center'
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      align: 'center'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (value: string) => (
        <Tag color='blue' style={{ fontSize: '14px', padding: '4px 12px' }}>
          {value?.toUpperCase() || 'UNKNOWN'}
        </Tag>
      )
    },
    {
      title: 'Địa chỉ XLKN',
      dataIndex: 'complaintAddress',
      key: 'complaintAddress',
      width: 150,
      align: 'center'
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      align: 'center'
    },
    {
      title: 'Mã SAP',
      dataIndex: 'sapCode',
      key: 'sapCode',
      width: 120,
      align: 'center'
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer',
      key: 'customer',
      width: 150,
      align: 'center'
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      align: 'center'
    },
    {
      title: 'NV theo dõi/giám sát',
      dataIndex: 'supervisor',
      key: 'supervisor',
      width: 150,
      align: 'center'
    },
    {
      title: 'NV được phân công',
      dataIndex: 'assignedStaff',
      key: 'assignedStaff',
      width: 150,
      align: 'center'
    },
    {
      title: 'Mức độ',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      align: 'center'
    },
    {
      title: 'Ngày bắt đầu',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 120,
      align: 'center'
    },
    {
      title: 'Ngày hết hạn',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      align: 'center'
    },
    {
      title: 'Ngày kết thúc',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 120,
      align: 'center'
    },
    {
      title: 'Thời gian checkin',
      dataIndex: 'checkinTime',
      key: 'checkinTime',
      width: 120,
      align: 'center'
    },
    {
      title: 'Thời gian checkout',
      dataIndex: 'checkoutTime',
      key: 'checkoutTime',
      width: 120,
      align: 'center'
    },
    {
      title: 'Trạng thái gửi tin',
      dataIndex: 'notificationStatus',
      key: 'notificationStatus',
      width: 120
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={false}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns as any}
            data={data}
            total={total}
            isLoading={false}
            onPageChange={handlePageChange}
            scroll={{ x: 3000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

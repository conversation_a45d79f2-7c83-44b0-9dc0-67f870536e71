{"quotation": {"title": "<PERSON><PERSON><PERSON><PERSON> lý báo giá", "create": "Tạo báo giá", "edit": "Chỉnh sửa báo giá", "detail": "<PERSON> tiết báo giá", "list": "<PERSON><PERSON> s<PERSON>ch báo giá", "filter": "Lọc báo giá", "search": "<PERSON><PERSON><PERSON> kiếm báo giá", "export": "<PERSON><PERSON><PERSON> b<PERSON>o giá", "import": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o giá", "send": "<PERSON><PERSON><PERSON> b<PERSON>o giá", "header": {"create_title": "<PERSON><PERSON><PERSON> y<PERSON>u cầu báo giá mới", "description": "<PERSON><PERSON><PERSON><PERSON> yêu cầu báo giá mới vào hệ thống", "item_list_info": "Thông tin sản phẩm", "customer": "<PERSON><PERSON><PERSON><PERSON>", "fromAddress": "<PERSON><PERSON><PERSON> chỉ g<PERSON>i hàng", "toAddress": "Địa chỉ nhận hàng", "goodsType": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "weight": "<PERSON><PERSON><PERSON><PERSON> (kg)", "packageCount": "Số lư<PERSON> ki<PERSON>n", "baseFee": "<PERSON><PERSON> vận chuyển c<PERSON> bản", "packingFee": "<PERSON><PERSON> đ<PERSON> gói", "insuranceFee": "<PERSON><PERSON> b<PERSON>", "storageFee": "<PERSON><PERSON> l<PERSON>u kho", "transportMethod": "<PERSON><PERSON><PERSON><PERSON> thức vận chuy<PERSON>n", "totalFee": "<PERSON><PERSON><PERSON>h<PERSON> (VNĐ)", "deliveryTime": "<PERSON><PERSON><PERSON><PERSON> gian giao hàng", "paymentTerm": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n thanh toán", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "note": "<PERSON><PERSON><PERSON>", "action": "Tác vụ", "itemInfo": "Thông tin item", "referenceSource": "<PERSON><PERSON><PERSON><PERSON> tham chi<PERSON>u"}, "place_holder": {"customer": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "fromAddress": "<PERSON><PERSON><PERSON> chỉ g<PERSON>i hàng", "toAddress": "Địa chỉ nhận hàng", "deliveryTime": "<PERSON><PERSON><PERSON><PERSON> gian giao hàng", "paymentTerm": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n thanh toán", "referenceSource": "<PERSON><PERSON><PERSON> nguồn tham chiếu", "externalMatGroup": "Chọn MatGroup", "transportMethod": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức vận chuyển", "goodsType": "<PERSON><PERSON><PERSON> d<PERSON> vụ"}, "columns_item": {"itemLine": "STT", "category": "<PERSON><PERSON>", "material": "<PERSON><PERSON><PERSON> p<PERSON>m", "shortText": "<PERSON><PERSON>", "quantity": "Số lượng", "deliveryDate": "<PERSON><PERSON><PERSON> giao", "plant": "<PERSON><PERSON>", "unit": "Đơn vị", "materialGroup": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "actions": "Tác vụ"}}}
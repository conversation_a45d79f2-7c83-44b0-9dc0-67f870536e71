import { SaveOutlined } from '@ant-design/icons'
import { Form, Input, Button, Radio, TreeSelect, Space, Divider, Row, Col } from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useState } from 'react'
import { toastService } from '~/services'
import { modalContent } from './add-or-edit-modal'

const { TextArea } = Input

interface CreateEvaluationCriteriaModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

interface TreeNode {
  key: string
  title: string
  value: string
  children?: TreeNode[]
}

const CreateEvaluationCriteriaModal = ({
  open,
  onClose,
  onSuccess
}: CreateEvaluationCriteriaModalProps) => {
  const [form] = useForm()

  const treeData: TreeNode[] = [
    {
      key: '1',
      title: '1. Quy mô hoạt động',
      value: '1',
      children: [
        {
          key: '1-1',
          title: '<PERSON><PERSON><PERSON> nghề',
          value: '1-1',
          children: [
            { key: '1-1-1', title: '<PERSON><PERSON>n xuất', value: '1-1-1' },
            { key: '1-1-2', title: 'Th<PERSON><PERSON>ng mại', value: '1-1-2' },
            { key: '1-1-3', title: 'Dịch vụ', value: '1-1-3' }
          ]
        },
        {
          key: '1-2',
          title: 'Thị trường của khách hàng',
          value: '1-2',
          children: [
            { key: '1-2-1', title: 'Nội địa', value: '1-2-1' },
            { key: '1-2-2', title: 'Xuất khẩu', value: '1-2-2' },
            { key: '1-2-3', title: 'Cả hai', value: '1-2-3' }
          ]
        },
        {
          key: '1-3',
          title: 'Số lượng nhân sự công ty',
          value: '1-3',
          children: [
            { key: '1-3-1', title: 'Dưới 50', value: '1-3-1' },
            { key: '1-3-2', title: '51-150', value: '1-3-2' },
            { key: '1-3-3', title: '151-300', value: '1-3-3' },
            { key: '1-3-4', title: '301-500', value: '1-3-4' },
            { key: '1-3-5', title: '501-1000', value: '1-3-5' },
            { key: '1-3-6', title: 'Trên 1000', value: '1-3-6' }
          ]
        },
        {
          key: '1-4',
          title: 'Chủ đầu tư nước ngoài',
          value: '1-4',
          children: [
            { key: '1-4-1', title: 'Có', value: '1-4-1' },
            { key: '1-4-2', title: 'Không', value: '1-4-2' }
          ]
        }
      ]
    },
    {
      key: '2',
      title: '2. Vị trí địa lý',
      value: '2',
      children: [
        {
          key: '2-1',
          title: 'Châu lục',
          value: '2-1',
          children: [
            { key: '2-1-1', title: 'Châu Á', value: '2-1-1' },
            { key: '2-1-2', title: 'Châu Âu', value: '2-1-2' },
            { key: '2-1-3', title: 'Châu Phi', value: '2-1-3' },
            { key: '2-1-4', title: 'Châu Mỹ', value: '2-1-4' },
            { key: '2-1-5', title: 'Châu Đại Dương', value: '2-1-5' },
            { key: '2-1-6', title: 'Châu Nam cực', value: '2-1-6' },
            { key: '2-1-7', title: 'Châu Nam Mỹ', value: '2-1-7' }
          ]
        },
        {
          key: '2-2',
          title: 'Khu vực',
          value: '2-2',
          children: [
            { key: '2-2-1', title: 'Bắc', value: '2-2-1' },
            { key: '2-2-2', title: 'Trung', value: '2-2-2' },
            { key: '2-2-3', title: 'Nam', value: '2-2-3' },
            { key: '2-2-4', title: 'Quận/Huyện', value: '2-2-4' },
            { key: '2-2-5', title: 'Phường/Xã', value: '2-2-5' }
          ]
        }
      ]
    },
    {
      key: '3',
      title: '3. Tài chính khách hàng',
      value: '3',
      children: [
        { key: '3-1', title: 'Doanh số', value: '3-1' },
        { key: '3-2', title: 'Công nợ', value: '3-2' },
        { key: '3-3', title: 'Công nợ trung bình quá hạn', value: '3-3' },
        {
          key: '3-4',
          title: 'Doanh thu trung bình trên 1 đơn hàng',
          value: '3-4'
        },
        {
          key: '3-5',
          title: 'Doanh thu luỹ kế so với cùng kỳ năm trước',
          value: '3-5'
        },
        { key: '3-6', title: 'Tăng trưởng Doanh số theo năm', value: '3-6' }
      ]
    },
    {
      key: '4',
      title: '4. Thói quen tiêu dùng',
      value: '4',
      children: [
        { key: '4-1', title: 'Thị hiếu khách', value: '4-1' },
        { key: '4-2', title: 'Tuổi khách hàng', value: '4-2' },
        {
          key: '4-3',
          title: 'Biết đến sản phẩm của công ty qua kênh nào?',
          value: '4-3'
        },
        { key: '4-4', title: 'Số đơn đặt hàng trong 1 năm', value: '4-4' }
      ]
    },
    {
      key: '5',
      title: '5. Thông tin cá nhân',
      value: '5',
      children: [
        {
          key: '5-1',
          title: 'Độ tuổi',
          value: '5-1',
          children: [
            { key: '5-1-1', title: '18 đến 25', value: '5-1-1' },
            { key: '5-1-2', title: '26 đến 40', value: '5-1-2' },
            { key: '5-1-3', title: '41 đến 50', value: '5-1-3' },
            { key: '5-1-4', title: 'Trên 50', value: '5-1-4' }
          ]
        },
        {
          key: '5-2',
          title: 'Giới tính',
          value: '5-2',
          children: [
            { key: '5-2-1', title: 'Nam', value: '5-2-1' },
            { key: '5-2-2', title: 'Nữ', value: '5-2-2' },
            { key: '5-2-3', title: 'Khác', value: '5-2-3' }
          ]
        },
        {
          key: '5-3',
          title: 'Quốc tịch',
          value: '5-3',
          children: [
            { key: '5-3-1', title: 'Việt Nam', value: '5-3-1' },
            { key: '5-3-2', title: 'Quốc tế', value: '5-3-2' }
          ]
        }
      ]
    }
  ]

  const handleSave = async (values: any) => {
    try {
      // TODO: Implement save API
      onClose()
      onSuccess?.()
      form.resetFields()
    } catch (error) {
      toastService.error('Tạo tiêu chí đánh giá thất bại')
    }
  }

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo bộ tiêu chí đánh giá mới'
      description='Thêm bộ tiêu chí đánh giá mới vào hệ thống'
      childrenBody={modalContent(false, form, handleSave, onClose)}
    />
  )
}

export default CreateEvaluationCriteriaModal

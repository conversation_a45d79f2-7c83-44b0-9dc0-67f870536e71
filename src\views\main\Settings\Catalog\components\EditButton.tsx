import { EditOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons'
import { Card, Row, Col, Form, Input, Button, Select, DatePicker, InputNumber } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'

import { IEmployee } from '~/dto/employee.dto'
import moment from 'moment'
import { useEffect } from 'react'
import { useModal } from '~/hooks/useModal'
import { Catalog } from '..'
import { NSCatalog } from '~/common/enums/NSCatalog'
import { useUpdateCatalog } from '~/hooks/catalog/useUpdateCatalog'
import { toastService } from '~/services'

const { Option } = Select
const { TextArea } = Input

interface EditButtonProps {
  data: Catalog
  onSuccess?: () => void
  onClose?: () => void
}

const EditButton = ({ data, onClose, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()
  const { mutateAsync: updateCatalog, isPending } = useUpdateCatalog()
  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data
      })
    }
  }, [open, data, form])
  const handleSave = async (values: any) => {
    updateCatalog({ ...data, ...values }).then(() => {
      closeModal()
    })
  }
  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical' onFinish={handleSave}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label='Tên sản phẩm'
                name='name'
                rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}>
                <Input placeholder='Nhập tên sản phẩm' />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item label='Loại sản phẩm' name='type'>
                <Select placeholder='Chọn loại sản phẩm'>
                  {Object.values(NSCatalog.EType).map((item) => (
                    <Select.Option key={item.code} value={item.code}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Nhóm sản phẩm' name='itemGroup'>
                <Select placeholder='Chọn nhóm sản phẩm' />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item
                label='Đơn vị tính'
                name='unit'
                rules={[{ required: true, message: 'Vui lòng chọn đơn vị tính' }]}>
                <Select placeholder='Chọn đơn vị tính'>
                  {Object.values(NSCatalog.EUnit).map((item) => (
                    <Select.Option key={item.code} value={item.code}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label='Đơn giá'
                name='unitPrice'
                rules={[{ required: true, message: 'Vui lòng nhập đơn giá' }]}>
                <InputNumber
                  min={0}
                  //format number
                  formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  style={{ width: '100%' }}
                  placeholder='Nhập đơn giá'
                />
              </Form.Item>
            </Col>

            {/* <Col span={8}>
              <Form.Item label='Hình ảnh sản phẩm' name='images'>
                <Input type='file' placeholder='Chọn hình ảnh sản phẩm' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Đính kèm' name='attachments'>
                <Input type='file' placeholder='Chọn file đính kèm' />
              </Form.Item>
            </Col> */}

            <Col span={24}>
              <Form.Item label='Mô tả' name='description'>
                <TextArea rows={4} placeholder='Nhập mô tả' />
              </Form.Item>
            </Col>
          </Row>
          <div
            style={{
              textAlign: 'right',
              marginTop: 24,
              borderTop: '1px solid #f0f0f0',
              paddingTop: 16
            }}>
            <Button onClick={closeModal} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
              Cập nhật
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type='primary' tooltip='Chỉnh sửa' />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Cập nhật sản phẩm'
        description='Cập nhật thông tin sản phẩm'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

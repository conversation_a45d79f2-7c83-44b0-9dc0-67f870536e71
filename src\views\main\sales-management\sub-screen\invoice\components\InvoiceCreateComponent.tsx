import { SaveOutlined } from '@ant-design/icons'
import { But<PERSON>, Card, Col, Form, Row, Select } from 'antd'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import BaseModal from '~/components/BaseModal'
import dayjs from 'dayjs'
import { ReloadOutlined } from '@ant-design/icons'

const { Option } = Select
export const InvoiceCreateComponent = ({ open, onClose, onSuccess }) => {
  const [useContract, setUseContract] = useState(false)
  const [form] = Form.useForm()
  const { t } = useTranslation()

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      console.log('Submitted values:', values)
      // Gọi API hoặc xử lý logic tạo nhóm ở đây
      onSuccess?.()
      onClose()
    } catch (err) {
      console.log('Validation failed:', err)
    }
  }
  const handleSwitchChange = (checked: boolean) => {
    setUseContract(checked)
  }

  const contentModal = (
    <Card>
      <Form layout='vertical' form={form} onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Chọn hợp đồng' name='useContract'>
              <Select placeholder='Chọn hợp đồng' onChange={handleSwitchChange} style={{ width: '100%' }}>
                <Option value=''>--Chọn hợp đồng--</Option>
                <Option value='HD-2025-056'>HD-2025-056</Option>
                <Option value='HD-2025-057'>HD-2025-057</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Chọn mẫu hóa đơn'>
              <Select placeholder='Chọn mẫu hóa đơn' style={{ width: '100%' }}>
                <Option value=''>--Chọn mẫu hóa đơn--</Option>
                <Option value='HD-2025-056'>HD-2025-056</Option>
                <Option value='HD-2025-057'>HD-2025-057</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center' }}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => form.resetFields()}
              style={{ marginRight: 8 }}>
              Làm mới
            </Button>
            <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
              Tạo hóa đơn
            </Button>
          </Col>
        </Row>
      </Form>
    </Card>
  )
  return (
    <BaseModal
      open={open}
      onClose={onClose}
      width={800}
      title='Tạo hóa đơn mới'
      description='Thêm hóa đơn mới vào hệ thống'
      childrenBody={contentModal}
    />
  )
}

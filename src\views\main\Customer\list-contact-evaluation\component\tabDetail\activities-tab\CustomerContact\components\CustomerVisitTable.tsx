import React from 'react'
import { Tag } from 'antd'
import { ICustomerVisit } from '~/dto/mission.dto'
import BaseTable from '~/components/BaseTable'

interface CustomerVisitTableProps {
  data: ICustomerVisit[]
  loading?: boolean
}

const CustomerVisitTable: React.FC<CustomerVisitTableProps> = ({
  data,
  loading = false
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hoàn thành':
        return 'success'
      case 'Đang thực hiện':
        return 'processing'
      case 'Chờ xử lý':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Thăm hỏi':
        return 'blue'
      case 'Khảo sát':
        return 'green'
      case 'Giới thiệu':
        return 'orange'
      case 'Hỗ trợ':
        return 'purple'
      case 'Đánh giá':
        return 'cyan'
      case 'Tư vấn':
        return 'magenta'
      case 'Khắc phục':
        return 'volcano'
      default:
        return 'default'
    }
  }

  const columns = [
    {
      title: 'STT',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1
    },
    {
      title: 'ID hoạt động',
      dataIndex: 'activityId',
      key: 'activityId',
      width: 120
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => <Tag color={getTypeColor(type)}>{type}</Tag>
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      )
    },
    {
      title: 'Người tạo',
      dataIndex: 'creator',
      key: 'creator',
      width: 120
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
      ellipsis: true
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      ellipsis: true
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 130
    }
  ]

  return (
    <div
      style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '16px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
      <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>
        Danh sách Thăm hỏi khách hàng
      </h3>
      <BaseTable
        columns={columns}
        data={data}
        total={data.length}
        isLoading={loading}
        scroll={{ x: 1500 }}
        rowKey='id'
      />
    </div>
  )
}

export default CustomerVisitTable

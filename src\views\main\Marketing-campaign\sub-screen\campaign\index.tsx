import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Collapse, DatePicker, Form, Input, Row, Select, Space, Tag } from 'antd'
import { EditOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import BaseTable from '~/components/BaseTable'
import { ColumnsType } from 'antd/es/table'
import { AnyObject } from 'antd/es/_util/type'
import { getColorStatus } from '~/common/utils/common.utils'

const { Option } = Select

const dataSource = [
  {
    key: '1',
    index: 1,
    campaignCode: '1594',
    campaignName: 'Tối ưu hóa vận hành kho hàng',
    contentName: 'Tối ưu hóa vận hành kho hàng',
    groupName: 'KH_NGÀY 06.06.2025_ALL DATA',
    status: 'sent',
    scheduleTime: '09/06/2025 10:00:00 AM',
    createdBy: '<PERSON><PERSON>',
    createdAt: '09/06/2025 08:40:50 AM'
  },
  {
    key: '2',
    index: 2,
    campaignCode: '1593',
    campaignName: 'Gi<PERSON>i pháp vận chuyển linh hoạt 2025',
    contentName: 'Giải pháp vận chuyển linh hoạt 2025',
    groupName: 'KH_NGÀY 07.05.2025_ALL DATA',
    status: 'sent',
    scheduleTime: '12/05/2025 10:00:00 AM',
    createdBy: 'Tả Linh',
    createdAt: '12/05/2025 09:09:05 AM'
  },
  {
    key: '3',
    index: 3,
    campaignCode: '1592',
    campaignName: 'Hệ thống theo dõi đơn hàng thông minh',
    contentName: 'Hệ thống theo dõi đơn hàng thông minh',
    groupName: 'Xuất khẩu - 25-02-2025',
    status: 'sent',
    scheduleTime: '10/05/2025 10:00:00 AM',
    createdBy: 'Tả Linh',
    createdAt: '10/05/2025 09:10:55 AM'
  },
  {
    key: '4',
    index: 4,
    campaignCode: '1591',
    campaignName: 'Quản lý chuỗi cung ứng chủ động',
    contentName: 'Quản lý chuỗi cung ứng chủ động',
    groupName: 'Xuất khẩu - 25-02-2025',
    status: 'sent',
    scheduleTime: '10/05/2025 10:00:00 AM',
    createdBy: 'Tả Linh',
    createdAt: '10/05/2025 09:10:55 AM'
  },
  {
    key: '5',
    index: 5,
    campaignCode: '1590',
    campaignName: 'Tăng tốc giao hàng chặng cuối',
    contentName: 'Tăng tốc giao hàng chặng cuối',
    groupName: 'Xuất khẩu - 25-02-2025',
    status: 'pending',
    scheduleTime: '10/05/2025 10:00:00 AM',
    createdBy: 'Tả Linh',
    createdAt: '10/05/2025 09:10:55 AM'
  },
  {
    key: '6',
    index: 6,
    campaignCode: '1589',
    campaignName: 'Ứng dụng công nghệ trong quản lý logistics',
    contentName: 'Ứng dụng công nghệ trong quản lý logistics',
    groupName: 'Xuất khẩu - 25-02-2025',
    status: 'sent',
    scheduleTime: '10/05/2025 10:00:00 AM',
    createdBy: 'Tả Linh',
    createdAt: '10/05/2025 09:10:55 AM'
  }
]



export const CampaignView = () => {
  const navigate = useNavigate()

  const handleClickRow = (record: any) => {
    navigate(`edit?id=${record.id}`)
  }

  const statusContent = (status: string) => {
    switch (status) {
      case 'sent':
        return 'Đã gửi'
      case 'pending':
        return 'Chờ gửi'
      default:
        return 'Đã gửi'
    }
  }

  const handleEditCampaign = (id: string) => {
    navigate(`edit?id=${id}`)
  }

  const columns: ColumnsType<AnyObject> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 60
    },
    {
      title: 'Mã chiến dịch',
      dataIndex: 'campaignCode',
      key: 'campaignCode'
    },
    {
      title: 'Tên chiến dịch',
      dataIndex: 'campaignName',
      key: 'campaignName',
      render: (text: string) => <span style={{ whiteSpace: 'pre-wrap' }}>{text}</span>
    },
    {
      title: 'Nội dung',
      dataIndex: 'contentName',
      key: 'contentName'
    },
    {
      title: 'Tên nhóm',
      dataIndex: 'groupName',
      key: 'groupName'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) =>
        status === 'sent' ? 
        <Tag color={getColorStatus(status)}>{statusContent(status).toUpperCase()}</Tag> : 
        <Tag color={getColorStatus(status)}>{statusContent(status).toUpperCase()}</Tag>
    },
    {
      title: 'Thời gian gửi',
      dataIndex: 'scheduleTime',
      key: 'scheduleTime'
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy'
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt'
    }
  ]

  const actionColumns: ColumnsType<AnyObject> = [
    {
      title: 'Tác vụ',
      width: 150,
      fixed: 'right',
      render: () => (
        <Space>
          <Button type='primary' icon={<EditOutlined />} onClick={() => handleEditCampaign('123123')}>
            Chỉnh sửa
          </Button>
        </Space>
      )
    }
  ]

  return (
    <Card title='Quản lý chiến dịch' style={{ width: '100%', height: '85vh', overflow: 'auto' }}>
      <Collapse>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label='Mã nội dụng' name='campaignCode'>
                <Input placeholder='Nhập mã nội dụng...' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Tên chiến dịch' name='campaignName'>
                <Input placeholder='Nhập tên chiến dịch...' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Trạng thái' name='status'>
                <Select allowClear placeholder='Trang thái'>
                  <Option value='sent'>Sent</Option>
                  <Option value='draft'>Draft</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Ngày tạo' name='createdAt'>
                <DatePicker style={{ width: '100%' }} format='DD/MM/YYYY' placeholder='Chọn ngày tạo...' />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space>
                <Button type='primary' htmlType='submit' icon={<SearchOutlined />}>
                  Tìm kiếm
                </Button>
                <Button htmlType='submit' icon={<ReloadOutlined />}>
                  Làm mới
                </Button>
              </Space>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>

      <BaseTable
        columns={[...columns, ...actionColumns]}
        data={dataSource}
        total={dataSource.length}
        isLoading={false}
        scroll={{ x: 2000 }}
        style={{ marginTop: 16 }}
      />
    </Card>
  )
}

import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { NSCatalog } from '~/common/enums/NSCatalog'
import { IFilter } from '~/components/BaseFilter/BaseFilter'

interface IProps {}
export const useCatalogFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    //pass values not null and undefined
    const newFilterData = Object.fromEntries(
      Object.entries(values).filter(([, value]) => value != null && value != undefined)
    )
    setFilterData(newFilterData)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'code',
      name: 'Mã sản phẩm',
      type: enumData.FILTER_TYPE.INPUT.key
    },
    {
      key: 'name',
      name: 'Tên sản phẩm',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //trạng thái
    {
      key: 'status',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: Object.values(NSCatalog.EStatus).map((status: any) => {
        return {
          value: status.code,
          name: status.name
        }
      })
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}

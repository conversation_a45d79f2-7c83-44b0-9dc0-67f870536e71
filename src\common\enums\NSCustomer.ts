export namespace NSCustomer {
  export enum ECustomerType {
    ADMIN = 'ADMIN',
    CUSTOMER = 'CUSTOMER'
  }
  export enum ECustomerStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE'
  }
}

export const ECustomer = {
  ECustomerStatus: {
    ACTIVE: {
      label: 'Active',
      value: NSCustomer.ECustomerStatus.ACTIVE,
      name: 'Hoạt động',
      color: 'green'
    },
    INACTIVE: {
      label: 'Inactive',
      value: NSCustomer.ECustomerStatus.INACTIVE,
      name: 'Không hoạt động',
      color: 'red'
    }
  },

  ECustomerType: {
    //  // Khách hàng chưa định danh
    //     NEW = 'NEW',
    //     // Khách hàng tiềm năng
    //     NURTURE = 'NURTURE',
    //     // Khách hàng đủ điều kiện
    //     QUALIFIED = 'QUALIFIED',
    //     // Khách hàng cơ hội
    //     OPPORTUNITY = 'OPPORTUNITY',
    //     // Khách hàng chính thức
    //     WON = 'WON',
    //     // Khách hàng chăm sóc & phát triển
    //     CARE = 'CARE',
    //     // Khách hàng không thành công (rời bỏ)
    //     LOST = 'LOST',
    //     // Khách hàng Lưu trữ
    //     ARCHIVED = 'ARCHIVED',

    NEW: {
      label: 'New',
      value: 'NEW',
      name: 'Khách hàng mới',
      color: 'blue'
    },
    NURTURE: {
      label: 'Nurture',
      value: 'NURTURE',
      name: 'Khách hàng tiềm năng',
      color: 'green'
    },
    QUALIFIED: {
      label: 'Qualified',
      value: 'QUALIFIED',
      name: 'Khách hàng đủ điều kiện',
      color: 'orange'
    },
    OPPORTUNITY: {
      label: 'Opportunity',
      value: 'OPPORTUNITY',
      name: 'Khách hàng cơ hội',
      color: 'red'
    },
    WON: {
      label: 'Won',
      value: 'WON',
      name: 'Khách hàng chính thức',
      color: 'purple'
    },
    CARE: {
      label: 'Care',
      value: 'CARE',
      name: 'Chăm sóc & phát triển',
      color: 'cyan'
    },
    LOST: {
      label: 'Lost',
      value: 'LOST',
      name: 'Khách hàng không thành công',
      color: 'magenta'
    },
    ARCHIVED: {
      label: 'Archived',
      value: 'ARCHIVED',
      name: 'Khách hàng lưu trữ',
      color: 'volcano'
    }
  }
}

import React, { useState } from 'react'
import { Table, Tag, Button, Space, Card, Select, Input, Col, Row, Popconfirm } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import {
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  SearchOutlined,
  StopOutlined
} from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'
import { useListCatalog } from '~/hooks/catalog/useListCatalog'
import BaseFilter from '~/components/BaseFilter/BaseFilter'
import { useCatalogFilterConfig } from './components/useCatalogFilterConfig'
import { NSCatalog } from '~/common/enums/NSCatalog'
import { BaseConfirmButton } from '~/components'
import { useSetActiveCatalog } from '~/hooks/catalog/useSetActiveCatalog'
import { useSetInactiveCatalog } from '~/hooks/catalog/useSetInactiveCatalog'

export interface Catalog {
  id?: string
  itemCategory?: string
  itemGroup?: string
  code?: string
  name?: string
  status?: 'active' | 'inactive'
  createdAt?: string
  createdBy?: string
}

export const CatalogView = () => {
  //filter

  const { filterData, filterFields, handleFilter, handleFilterReset } = useCatalogFilterConfig()
  const { data, isLoading, total } = useListCatalog(filterData)
  //set active
  const { mutateAsync: setActiveCatalog, isPending: isSetActivePending } = useSetActiveCatalog()
  const { mutateAsync: setInactiveCatalog, isPending: isSetInactivePending } =
    useSetInactiveCatalog()
  const columns: ColumnsType<Catalog> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_text, _record, index) => index + 1
    },
    //loại sản phẩm
    // {
    //   title: 'Loại sản phẩm',
    //   dataIndex: 'itemCategory',
    //   key: 'itemCategory'
    // },
    // //nhóm sản phẩm
    // {
    //   title: 'Nhóm sản phẩm',
    //   dataIndex: 'itemGroup',
    //   key: 'itemGroup'
    // },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code'
    },

    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name'
    },

    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === NSCatalog.EStatus.ACTIVE.code ? 'green' : 'red'
        const label =
          status === NSCatalog.EStatus.ACTIVE.code ? 'Đang hoạt động' : 'Ngưng hoạt động'

        return <Tag color={color}>{label}</Tag>
      }
    },

    {
      title: 'Tác vụ',
      key: 'action',
      render: (_, record) => (
        <Space>
          <DetailButton id={record.id} />
          <EditButton data={record} />
          <BaseConfirmButton
            icon={
              record.status === NSCatalog.EStatus.ACTIVE.code ? (
                <StopOutlined />
              ) : (
                <PlayCircleOutlined />
              )
            }
            tooltip={
              record.status === NSCatalog.EStatus.ACTIVE.code ? 'Ngưng hoạt động' : 'Hoạt động lại'
            }
            confirmTitle={
              record.status === NSCatalog.EStatus.ACTIVE.code
                ? 'Bạn có chắc muốn ngưng hoạt động sản phẩm này?'
                : 'Bạn có chắc muốn hoạt động lại sản phẩm này?'
            }
            type='default'
            danger={record.status === NSCatalog.EStatus.ACTIVE.code}
            onConfirm={() => {
              if (record.status === NSCatalog.EStatus.ACTIVE.code) {
                setInactiveCatalog({ id: record.id })
              } else {
                setActiveCatalog({ id: record.id })
              }
            }}
          />
        </Space>
      )
    }
  ]

  return (
    <BaseView>
      <Card title='Thiết lập sản phẩm'>
        {/* Bộ lọc */}
        <BaseFilter
          onFilter={handleFilter}
          onReset={handleFilterReset}
          isLoading={isLoading}
          filters={filterFields}
        />
        <BaseTable
          columns={columns}
          data={data || []}
          total={total || 0}
          isLoading={isLoading}
          rowKey='key'
        />
      </Card>
    </BaseView>
  )
}

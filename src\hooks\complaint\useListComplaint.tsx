import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import {
  EComplaintPriority,
  EComplaintStatus,
  EComplaintType,
  ENotificationStatus,
  IComplaint,
  IComplaintResponse
} from '~/dto/complaint.dto'

interface UseListComplaintParams {}

export const useListComplaint = (params: UseListComplaintParams) => {
  const data: IComplaintResponse = {
    data: [
      {
        id: '1',
        title: 'Khiếu nại về chất lượng sản phẩm',
        description: 'Sản phẩm bị lỗi sau khi sử dụng 2 ngày',
        type: EComplaintType.PRODUCT,
        status: EComplaintStatus.PENDING,
        priority: EComplaintPriority.HIGH,
        complaintAddress: '123 <PERSON>uy<PERSON>n <PERSON>, Qu<PERSON>n 7, TP.HCM',
        address: '123 <PERSON><PERSON><PERSON><PERSON>, Quận 7, TP.HCM',
        customerCode: 'KH001',
        customer: 'Công ty TNHH ABC',
        customerId: '1',
        sapCode: 'SAP001',
        supervisor: '<PERSON><PERSON><PERSON><PERSON>',
        supervisorId: '1',
        assignedStaff: 'Trần Thị B',
        assignedStaffId: '2',
        startDate: '2024-03-20',
        dueDate: '2024-03-25',
        endDate: '',
        checkinTime: '09:00',
        checkoutTime: '',
        notificationStatus: ENotificationStatus.PENDING,
        media: [],
        notes: 'Khách hàng yêu cầu đổi sản phẩm mới',
        solution: '',
        rating: 0,
        feedback: ''
      },
      {
        id: '2',
        title: 'Khiếu nại về dịch vụ hậu mãi',
        description: 'Nhân viên kỹ thuật đến trễ giờ hẹn',
        type: EComplaintType.SERVICE,
        status: EComplaintStatus.PROCESSING,
        priority: EComplaintPriority.MEDIUM,
        complaintAddress: '456 Lê Lợi, Quận 1, TP.HCM',
        address: '456 Lê Lợi, Quận 1, TP.HCM',
        customerCode: 'KH002',
        customer: 'Công ty TNHH XYZ',
        customerId: '2',
        sapCode: 'SAP002',
        supervisor: 'Lê Văn C',
        supervisorId: '3',
        assignedStaff: 'Phạm Thị D',
        assignedStaffId: '4',
        startDate: '2024-03-19',
        dueDate: '2024-03-24',
        endDate: '',
        checkinTime: '14:00',
        checkoutTime: '',
        notificationStatus: ENotificationStatus.SENT,
        media: [],
        notes: 'Đã liên hệ với khách hàng và xin lỗi',
        solution: 'Điều chỉnh lịch hẹn mới',
        rating: 0,
        feedback: ''
      },
      {
        id: '3',
        title: 'Lỗi kỹ thuật hệ thống',
        description: 'Hệ thống bị treo khi xử lý dữ liệu lớn',
        type: EComplaintType.TECHNICAL,
        status: EComplaintStatus.COMPLETED,
        priority: EComplaintPriority.URGENT,
        complaintAddress: '789 Đồng Khởi, Quận 1, TP.HCM',
        address: '789 Đồng Khởi, Quận 1, TP.HCM',
        customerCode: 'KH003',
        customer: 'Công ty Cổ phần DEF',
        customerId: '3',
        sapCode: 'SAP003',
        supervisor: 'Hoàng Văn E',
        supervisorId: '5',
        assignedStaff: 'Nguyễn Thị F',
        assignedStaffId: '6',
        startDate: '2024-03-18',
        dueDate: '2024-03-23',
        endDate: '2024-03-22',
        checkinTime: '10:00',
        checkoutTime: '16:00',
        notificationStatus: ENotificationStatus.SENT,
        media: [],
        notes: 'Đã khắc phục lỗi và tối ưu hệ thống',
        solution: 'Nâng cấp phần cứng và tối ưu code',
        rating: 4,
        feedback: 'Hài lòng với cách xử lý'
      },
      {
        id: '4',
        title: 'Khiếu nại về hóa đơn',
        description: 'Hóa đơn không khớp với số tiền thanh toán',
        type: EComplaintType.BILLING,
        status: EComplaintStatus.CANCELLED,
        priority: EComplaintPriority.LOW,
        complaintAddress: '321 Nguyễn Huệ, Quận 1, TP.HCM',
        address: '321 Nguyễn Huệ, Quận 1, TP.HCM',
        customerCode: 'KH004',
        customer: 'Công ty TNHH GHI',
        customerId: '4',
        sapCode: 'SAP004',
        supervisor: 'Trần Văn G',
        supervisorId: '7',
        assignedStaff: 'Lê Thị H',
        assignedStaffId: '8',
        startDate: '2024-03-17',
        dueDate: '2024-03-22',
        endDate: '2024-03-20',
        checkinTime: '11:00',
        checkoutTime: '15:00',
        notificationStatus: ENotificationStatus.FAILED,
        media: [],
        notes: 'Khách hàng hủy khiếu nại',
        solution: 'Đã xác nhận lại hóa đơn chính xác',
        rating: 0,
        feedback: ''
      },
      {
        id: '5',
        title: 'Yêu cầu bảo trì định kỳ',
        description: 'Cần bảo trì hệ thống theo lịch',
        type: EComplaintType.SERVICE,
        status: EComplaintStatus.PROCESSING,
        priority: EComplaintPriority.MEDIUM,
        complaintAddress: '654 Lê Duẩn, Quận 1, TP.HCM',
        address: '654 Lê Duẩn, Quận 1, TP.HCM',
        customerCode: 'KH005',
        customer: 'Công ty TNHH JKL',
        customerId: '5',
        sapCode: 'SAP005',
        supervisor: 'Phạm Văn I',
        supervisorId: '9',
        assignedStaff: 'Hoàng Thị K',
        assignedStaffId: '10',
        startDate: '2024-03-16',
        dueDate: '2024-03-21',
        endDate: '',
        checkinTime: '13:00',
        checkoutTime: '',
        notificationStatus: ENotificationStatus.PENDING,
        media: [],
        notes: 'Đã lên lịch bảo trì',
        solution: '',
        rating: 0,
        feedback: ''
      },
      {
        id: '6',
        title: 'Khiếu nại về thời gian giao hàng',
        description: 'Giao hàng chậm hơn cam kết',
        type: EComplaintType.SERVICE,
        status: EComplaintStatus.REJECTED,
        priority: EComplaintPriority.HIGH,
        complaintAddress: '987 Nguyễn Đình Chiểu, Quận 3, TP.HCM',
        address: '987 Nguyễn Đình Chiểu, Quận 3, TP.HCM',
        customerCode: 'KH006',
        customer: 'Công ty TNHH MNO',
        customerId: '6',
        sapCode: 'SAP006',
        supervisor: 'Lê Văn L',
        supervisorId: '11',
        assignedStaff: 'Trần Thị M',
        assignedStaffId: '12',
        startDate: '2024-03-15',
        dueDate: '2024-03-20',
        endDate: '2024-03-18',
        checkinTime: '09:30',
        checkoutTime: '16:30',
        notificationStatus: ENotificationStatus.SENT,
        media: [],
        notes: 'Do thời tiết xấu nên giao hàng chậm',
        solution: 'Bồi thường phí giao hàng',
        rating: 2,
        feedback: 'Không hài lòng với cách xử lý'
      },
      {
        id: '7',
        title: 'Lỗi phần mềm',
        description: 'Ứng dụng bị crash khi đăng nhập',
        type: EComplaintType.TECHNICAL,
        status: EComplaintStatus.COMPLETED,
        priority: EComplaintPriority.URGENT,
        complaintAddress: '147 Võ Văn Tần, Quận 3, TP.HCM',
        address: '147 Võ Văn Tần, Quận 3, TP.HCM',
        customerCode: 'KH007',
        customer: 'Công ty TNHH PQR',
        customerId: '7',
        sapCode: 'SAP007',
        supervisor: 'Nguyễn Văn N',
        supervisorId: '13',
        assignedStaff: 'Phạm Thị O',
        assignedStaffId: '14',
        startDate: '2024-03-14',
        dueDate: '2024-03-19',
        endDate: '2024-03-17',
        checkinTime: '10:30',
        checkoutTime: '15:30',
        notificationStatus: ENotificationStatus.SENT,
        media: [],
        notes: 'Đã phát hiện và sửa lỗi',
        solution: 'Cập nhật phiên bản mới',
        rating: 5,
        feedback: 'Rất hài lòng với cách xử lý'
      },
      {
        id: '8',
        title: 'Khiếu nại về giá sản phẩm',
        description: 'Giá sản phẩm cao hơn thị trường',
        type: EComplaintType.PRODUCT,
        status: EComplaintStatus.PENDING,
        priority: EComplaintPriority.LOW,
        complaintAddress: '258 Lý Tự Trọng, Quận 1, TP.HCM',
        address: '258 Lý Tự Trọng, Quận 1, TP.HCM',
        customerCode: 'KH008',
        customer: 'Công ty TNHH STU',
        customerId: '8',
        sapCode: 'SAP008',
        supervisor: 'Hoàng Văn P',
        supervisorId: '15',
        assignedStaff: 'Lê Thị Q',
        assignedStaffId: '16',
        startDate: '2024-03-13',
        dueDate: '2024-03-18',
        endDate: '',
        checkinTime: '14:30',
        checkoutTime: '',
        notificationStatus: ENotificationStatus.PENDING,
        media: [],
        notes: 'Đang xem xét lại chính sách giá',
        solution: '',
        rating: 0,
        feedback: ''
      },
      {
        id: '9',
        title: 'Yêu cầu đào tạo sử dụng',
        description: 'Cần đào tạo nhân viên sử dụng phần mềm mới',
        type: EComplaintType.SERVICE,
        status: EComplaintStatus.PROCESSING,
        priority: EComplaintPriority.MEDIUM,
        complaintAddress: '369 Điện Biên Phủ, Quận 3, TP.HCM',
        address: '369 Điện Biên Phủ, Quận 3, TP.HCM',
        customerCode: 'KH009',
        customer: 'Công ty TNHH VWX',
        customerId: '9',
        sapCode: 'SAP009',
        supervisor: 'Trần Văn R',
        supervisorId: '17',
        assignedStaff: 'Nguyễn Thị S',
        assignedStaffId: '18',
        startDate: '2024-03-12',
        dueDate: '2024-03-17',
        endDate: '',
        checkinTime: '11:30',
        checkoutTime: '',
        notificationStatus: ENotificationStatus.SENT,
        media: [],
        notes: 'Đã lên kế hoạch đào tạo',
        solution: '',
        rating: 0,
        feedback: ''
      },
      {
        id: '10',
        title: 'Khiếu nại về chất lượng dịch vụ',
        description: 'Dịch vụ không đạt yêu cầu như cam kết',
        type: EComplaintType.SERVICE,
        status: EComplaintStatus.COMPLETED,
        priority: EComplaintPriority.HIGH,
        complaintAddress: '741 Nguyễn Trãi, Quận 5, TP.HCM',
        address: '741 Nguyễn Trãi, Quận 5, TP.HCM',
        customerCode: 'KH010',
        customer: 'Công ty TNHH YZ',
        customerId: '10',
        sapCode: 'SAP010',
        supervisor: 'Phạm Văn T',
        supervisorId: '19',
        assignedStaff: 'Hoàng Thị U',
        assignedStaffId: '20',
        startDate: '2024-03-11',
        dueDate: '2024-03-16',
        endDate: '2024-03-15',
        checkinTime: '09:00',
        checkoutTime: '17:00',
        notificationStatus: ENotificationStatus.SENT,
        media: [],
        notes: 'Đã cải thiện chất lượng dịch vụ',
        solution: 'Bồi thường và cam kết chất lượng',
        rating: 3,
        feedback: 'Chấp nhận được'
      }
    ],
    total: 10
  }

  return {
    data: data.data,
    isLoading: false,
    refetch: () => {},
    total: data.total
  }
}

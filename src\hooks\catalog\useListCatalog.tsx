import { useInfiniteQuery } from '@tanstack/react-query'
import { IProduct } from '~/dto/product.dto'
import { rootApiService } from '~/services/@common'
import { endpoint_catalog } from '~/services/endpoints'
import { PageResponse } from '~/@ui/GridControl/models'

interface UseListProductParams {
  code?: string
  name?: string
  status?: string
  pageIndex?: number
  pageSize?: number
}

export const useListCatalog = (params: UseListProductParams) => {
  const { data, isLoading, refetch } = useInfiniteQuery<any, Error>({
    queryKey: [
      endpoint_catalog.list,
      {
        ...params,
        pageSize: params.pageSize,
        pageIndex: params.pageIndex
      }
    ],
    queryFn: () =>
      rootApiService.get(endpoint_catalog.list, {
        ...params,
        pageSize: params.pageSize || 10,
        pageIndex: params.pageIndex || 1
      }),
    getNextPageParam: (lastPage, allPages) =>
      lastPage.data.length > 0 ? allPages.length + 1 : undefined,
    initialPageParam: 1
  })

  const formatData = data?.pages.flatMap((page) => page.data) ?? []
  const total = data?.pages[0]?.total ?? 0
  return {
    data: formatData,
    isLoading: isLoading,
    refetch,
    total: total
  }
}

import { EyeOutlined } from '@ant-design/icons'
import { Card, Typography, Row, Col, Divider, Steps } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom } from '~/common/helper/helper'
import { IMission } from '~/dto/missions.dto'
import { useEffect, useState } from 'react'
import { IEmployee } from '~/dto/employee.dto'
import { useModal } from '~/hooks/useModal'
import { Invoice } from '..'

const { Title, Paragraph, Text } = Typography

interface DetailButtonProps {
  data: Invoice
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  const modalContent = (
    <div>
      {/* Product Overview Card */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Row gutter={16}>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Mã:</Text>
            <br />
            <Text>{data.code}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tên:</Text>
            <br />
            <Text>{data.customerName}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Đơn vị phát hành:</Text>
            <br />
            <Text>{data.issuingUnit}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Loại dịch vụ:</Text>
            <br />
            <Text>{data.serviceType}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text>
            <br />
            <Text>{data.status}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày tạo:</Text>
            <br />
            <Text>{formatDateCustom(data.createdAt, 'DD/MM/YYYY')}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Người tạo:</Text>
            <br />
            <Text>{data.createdBy}</Text>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EyeOutlined />}
        onClick={openModal}
        type='primary'
        tooltip='Xem chi tiết'
        children='Xem chi tiết'
      />
      <BaseModal
        open={open}
        title='Chi tiết mẫu hóa đơn'
        description='Thông tin chi tiết mẫu hóa đơn'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton

{"quotation": {"title": "Quotation Management", "create": "Create Quotation", "edit": "Edit Quotation", "detail": "Quotation Detail", "list": "Quotation List", "filter": "Filter Quotation", "search": "Search Quotation", "export": "Export Quotation", "import": "Import Quotation", "send": "Send Quotation", "header": {"create_title": "Create New Quotation", "description": "Create new quotation request", "item_list_info": "Product List", "customer": "Customer", "fromAddress": "From Address", "toAddress": "To Address", "goodsType": "Goods Type", "weight": "Weight (kg)", "packageCount": "Package Count", "baseFee": "Base Fee", "packingFee": "Packing Fee", "insuranceFee": "Insurance Fee", "storageFee": "Storage Fee", "transportMethod": "Transport Method", "totalFee": "Total Fee (VND)", "deliveryTime": "Delivery Time", "paymentTerm": "Payment Term", "status": "Status", "note": "Note", "action": "Action", "itemInfo": "Item Information", "referenceSource": "Reference Source"}, "place_holder": {"customer": "Select Customer", "fromAddress": "From Address", "toAddress": "To Address", "deliveryTime": "Delivery Time", "paymentTerm": "Payment Term", "referenceSource": "Select Reference Source", "externalMatGroup": "Select MatGroup", "transportMethod": "Select Transport Method", "goodsType": "Select Goods Type"}, "columns_item": {"itemLine": "Item Line", "category": "Category", "material": "Material", "shortText": "Short Text", "quantity": "Quantity", "deliveryDate": "Delivery Date", "plant": "Plant", "unit": "Unit", "materialGroup": "Material Group", "actions": "Actions"}}}
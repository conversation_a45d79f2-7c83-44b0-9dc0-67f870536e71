import { Row, Col, Tag, Tooltip, Typography, Space } from 'antd'
import { useState } from 'react'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import {
  IFilterReportKpiSaleEmployee,
  IReportKpiSaleEmployee
} from '~/dto/report-kpi-sale-employee.dto'
import FilterProduct from './components/FilterProduct'
import { getColorPercent } from '~/common/utils/common.utils'
import { BaseText } from '~/components'

const { Text } = Typography

export const SalesReportKPIView = () => {
  const [filter, setFilter] = useState<IFilterReportKpiSaleEmployee>({
    pageIndex: 1,
    pageSize: 10
  })

  const columns: any[] = [
    { title: 'STT', key: 'stt', width: 60, align: 'center', render: (_, __, index) => index + 1 },
    { title: 'Mã NV', dataIndex: 'key', key: 'key', align: 'center', width: 100 },
    { title: 'Nhân viên', dataIndex: 'name', key: 'name', align: 'center', width: 200 },
    {
      title: (
        <Tooltip title='Số lượng khách hàng mới mà nhân viên đã tạo trên hệ thống CRM (chưa từng có trước đó).'>
          <Text strong> Số KH mới</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'newCustomersTarget',
          key: 'newCustomersTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'newCustomersActual',
          key: 'newCustomersActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'newCustomersRate',
          key: 'newCustomersRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng số cuộc gọi điện, cuộc gặp, hoặc demo giới thiệu sản phẩm/dịch vụ đến khách hàng.'>
          <Text strong> Cuộc gọi/Gặp KH</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'callsTarget',
          key: 'callsTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'callsActual',
          key: 'callsActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'callsRate',
          key: 'callsRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
        }
      ]
    },

    {
      title: (
        <Tooltip title='Số báo giá đã được gửi cho khách hàng (qua CRM, email,...), phản ánh hoạt động tiếp cận.'>
          <Text strong> Báo giá gửi đi</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'quotesTarget',
          key: 'quotesTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'quotesActual',
          key: 'quotesActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'quotesRate',
          key: 'quotesRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng giá trị tiền từ các hợp đồng đã được ký kết với khách hàng trong kỳ báo cáo.'>
          <Text strong> Doanh số hợp đồng</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'ordersTarget',
          key: 'ordersTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'ordersActual',
          key: 'ordersActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'ordersRate',
          key: 'ordersRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
        }
      ]
    },

    {
      title: (
        <Tooltip title='Tỷ lệ số cơ hội bán hàng đã chuyển đổi thành hợp đồng chính thức.'>
          <Text strong> Tỷ lệ chốt đơn</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'closingRateTarget',
          key: 'closingRateTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'closingRateActual',
          key: 'closingRateActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'closingRateRate',
          key: 'closingRateRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
        }
      ]
    },

    {
      title: (
        <Tooltip title='Tỷ lệ cơ hội, khách hàng hoặc deal được cập nhật đầy đủ trạng thái, thông tin, ghi chú,... trên CRM.'>
          <Text strong> Tỷ lệ cập nhật CRM</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'crmUpdateTarget',
          key: 'crmUpdateTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'crmUpdateActual',
          key: 'crmUpdateActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'crmUpdateRate',
          key: 'crmUpdateRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng điểm KPI = ∑ (Tỷ lệ hoàn thành KPI × Trọng số) / 100'>
          <Text strong> Tổng điểm KPI</Text>
        </Tooltip>
      ),
      align: 'center',
      dataIndex: 'totalKpi',
      key: 'totalKpi',
      width: 150,
      render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
    }
  ]

  const fakeData: IReportKpiSaleEmployee[] = [
    // 5 provided records
    {
      key: 'NV0000-1',
      name: 'Nguyễn Văn A',
      newCustomersTarget: 20,
      newCustomersActual: 21,
      newCustomersRate: '105%',
      callsTarget: 50,
      callsActual: 40,
      callsRate: '80%',
      quotesTarget: 30,
      quotesActual: 31,
      quotesRate: '103%',
      ordersTarget: 30,
      ordersActual: 31,
      ordersRate: '103%',
      revenueTarget: 500000000,
      revenueActual: 40000000,
      revenueRate: '8%',
      closingRateTarget: '20%',
      closingRateActual: '15%',
      closingRateRate: '75%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '59%',
      crmUpdateRate: '62%',
      totalKpi: '63.05%'
    },
    {
      key: 'NV0000-2',
      name: 'Trần Thị B',
      newCustomersTarget: 20,
      newCustomersActual: 21,
      newCustomersRate: '105%',
      callsTarget: 50,
      callsActual: 55,
      callsRate: '110%',
      quotesTarget: 30,
      quotesActual: 28,
      quotesRate: '93%',
      ordersTarget: 30,
      ordersActual: 28,
      ordersRate: '93%',
      revenueTarget: 500000000,
      revenueActual: 200000000,
      revenueRate: '40%',
      closingRateTarget: '20%',
      closingRateActual: '18%',
      closingRateRate: '90%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '90%',
      crmUpdateRate: '95%',
      totalKpi: '81.2%'
    },
    {
      key: 'NV0000-3',
      name: 'Lê Hoàng C',
      newCustomersTarget: 20,
      newCustomersActual: 21,
      newCustomersRate: '105%',
      callsTarget: 50,
      callsActual: 48,
      callsRate: '96%',
      quotesTarget: 30,
      quotesActual: 35,
      quotesRate: '117%',
      ordersTarget: 30,
      ordersActual: 35,
      ordersRate: '117%',
      revenueTarget: 500000000,
      revenueActual: 600000000,
      revenueRate: '120%',
      closingRateTarget: '20%',
      closingRateActual: '25%',
      closingRateRate: '125%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '98%',
      crmUpdateRate: '103%',
      totalKpi: '112.75%'
    },
    {
      key: 'NV0000-4',
      name: 'Phạm Mai D',
      newCustomersTarget: 20,
      newCustomersActual: 21,
      newCustomersRate: '105%',
      callsTarget: 50,
      callsActual: 30,
      callsRate: '60%',
      quotesTarget: 30,
      quotesActual: 25,
      quotesRate: '83%',
      ordersTarget: 30,
      ordersActual: 25,
      ordersRate: '83%',
      revenueTarget: 500000000,
      revenueActual: 300000000,
      revenueRate: '60%',
      closingRateTarget: '20%',
      closingRateActual: '12%',
      closingRateRate: '60%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '70%',
      crmUpdateRate: '74%',
      totalKpi: '71.6%'
    },
    {
      key: 'NV0000-5',
      name: 'Hoàng Văn E',
      newCustomersTarget: 20,
      newCustomersActual: 21,
      newCustomersRate: '105%',
      callsTarget: 50,
      callsActual: 40,
      callsRate: '80%',
      quotesTarget: 30,
      quotesActual: 31,
      quotesRate: '103%',
      ordersTarget: 30,
      ordersActual: 31,
      ordersRate: '103%',
      revenueTarget: 500000000,
      revenueActual: 40000000,
      revenueRate: '8%',
      closingRateTarget: '20%',
      closingRateActual: '15%',
      closingRateRate: '75%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '59%',
      crmUpdateRate: '62%',
      totalKpi: '63.05%'
    },
    // 10 random records
    {
      key: 'NV0000-6',
      name: 'NV F1',
      newCustomersTarget: 25,
      newCustomersActual: 23,
      newCustomersRate: '92%',
      callsTarget: 60,
      callsActual: 55,
      callsRate: '92%',
      quotesTarget: 35,
      quotesActual: 30,
      quotesRate: '86%',
      ordersTarget: 35,
      ordersActual: 30,
      ordersRate: '86%',
      revenueTarget: 500000000,
      revenueActual: 450000000,
      revenueRate: '90%',
      closingRateTarget: '25%',
      closingRateActual: '20%',
      closingRateRate: '80%',
      crmUpdateTarget: '90%',
      crmUpdateActual: '85%',
      crmUpdateRate: '94%',
      totalKpi: '86.56%'
    },
    {
      key: 'NV0000-7',
      name: 'NV F2',
      newCustomersTarget: 18,
      newCustomersActual: 20,
      newCustomersRate: '111%',
      callsTarget: 45,
      callsActual: 40,
      callsRate: '89%',
      quotesTarget: 28,
      quotesActual: 25,
      quotesRate: '89%',
      ordersTarget: 28,
      ordersActual: 25,
      ordersRate: '89%',
      revenueTarget: 500000000,
      revenueActual: 380000000,
      revenueRate: '76%',
      closingRateTarget: '18%',
      closingRateActual: '16%',
      closingRateRate: '89%',
      crmUpdateTarget: '92%',
      crmUpdateActual: '90%',
      crmUpdateRate: '98%',
      totalKpi: '83.02%'
    },
    {
      key: 'NV0000-8',
      name: 'NV F3',
      newCustomersTarget: 30,
      newCustomersActual: 28,
      newCustomersRate: '93%',
      callsTarget: 70,
      callsActual: 65,
      callsRate: '93%',
      quotesTarget: 40,
      quotesActual: 35,
      quotesRate: '88%',
      ordersTarget: 40,
      ordersActual: 35,
      ordersRate: '88%',
      revenueTarget: 500000000,
      revenueActual: 500000000,
      revenueRate: '100%',
      closingRateTarget: '30%',
      closingRateActual: '28%',
      closingRateRate: '93%',
      crmUpdateTarget: '96%',
      crmUpdateActual: '94%',
      crmUpdateRate: '98%',
      totalKpi: '94.44%'
    },
    {
      key: 'NV0000-9',
      name: 'NV F4',
      newCustomersTarget: 22,
      newCustomersActual: 20,
      newCustomersRate: '91%',
      callsTarget: 55,
      callsActual: 50,
      callsRate: '91%',
      quotesTarget: 32,
      quotesActual: 30,
      quotesRate: '94%',
      ordersTarget: 32,
      ordersActual: 30,
      ordersRate: '94%',
      revenueTarget: 500000000,
      revenueActual: 300000000,
      revenueRate: '60%',
      closingRateTarget: '22%',
      closingRateActual: '20%',
      closingRateRate: '91%',
      crmUpdateTarget: '94%',
      crmUpdateActual: '92%',
      crmUpdateRate: '98%',
      totalKpi: '82.59%'
    },
    {
      key: 'NV0000-10',
      name: 'NV F5',
      newCustomersTarget: 24,
      newCustomersActual: 26,
      newCustomersRate: '108%',
      callsTarget: 58,
      callsActual: 60,
      callsRate: '103%',
      quotesTarget: 36,
      quotesActual: 38,
      quotesRate: '106%',
      ordersTarget: 36,
      ordersActual: 38,
      ordersRate: '106%',
      revenueTarget: 500000000,
      revenueActual: 550000000,
      revenueRate: '110%',
      closingRateTarget: '24%',
      closingRateActual: '25%',
      closingRateRate: '104%',
      crmUpdateTarget: '93%',
      crmUpdateActual: '95%',
      crmUpdateRate: '102%',
      totalKpi: '107.82%'
    },
    {
      key: 'NV0000-11',
      name: 'NV F6',
      newCustomersTarget: 20,
      newCustomersActual: 18,
      newCustomersRate: '90%',
      callsTarget: 50,
      callsActual: 48,
      callsRate: '96%',
      quotesTarget: 30,
      quotesActual: 27,
      quotesRate: '90%',
      ordersTarget: 30,
      ordersActual: 27,
      ordersRate: '90%',
      revenueTarget: 500000000,
      revenueActual: 250000000,
      revenueRate: '50%',
      closingRateTarget: '20%',
      closingRateActual: '18%',
      closingRateRate: '90%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '90%',
      crmUpdateRate: '95%',
      totalKpi: '74.1%'
    },
    {
      key: 'NV0000-12',
      name: 'NV F7',
      newCustomersTarget: 26,
      newCustomersActual: 28,
      newCustomersRate: '108%',
      callsTarget: 65,
      callsActual: 60,
      callsRate: '92%',
      quotesTarget: 38,
      quotesActual: 35,
      quotesRate: '92%',
      ordersTarget: 38,
      ordersActual: 35,
      ordersRate: '92%',
      revenueTarget: 500000000,
      revenueActual: 520000000,
      revenueRate: '104%',
      closingRateTarget: '26%',
      closingRateActual: '28%',
      closingRateRate: '108%',
      crmUpdateTarget: '97%',
      crmUpdateActual: '96%',
      crmUpdateRate: '99%',
      totalKpi: '102.88%'
    },
    {
      key: 'NV0000-13',
      name: 'NV F8',
      newCustomersTarget: 28,
      newCustomersActual: 30,
      newCustomersRate: '107%',
      callsTarget: 68,
      callsActual: 70,
      callsRate: '103%',
      quotesTarget: 40,
      quotesActual: 42,
      quotesRate: '105%',
      ordersTarget: 40,
      ordersActual: 42,
      ordersRate: '105%',
      revenueTarget: 500000000,
      revenueActual: 600000000,
      revenueRate: '120%',
      closingRateTarget: '28%',
      closingRateActual: '30%',
      closingRateRate: '107%',
      crmUpdateTarget: '98%',
      crmUpdateActual: '99%',
      crmUpdateRate: '101%',
      totalKpi: '113.55%'
    },
    {
      key: 'NV0000-14',
      name: 'NV F9',
      newCustomersTarget: 21,
      newCustomersActual: 22,
      newCustomersRate: '105%',
      callsTarget: 55,
      callsActual: 50,
      callsRate: '91%',
      quotesTarget: 33,
      quotesActual: 30,
      quotesRate: '91%',
      ordersTarget: 33,
      ordersActual: 30,
      ordersRate: '91%',
      revenueTarget: 500000000,
      revenueActual: 450000000,
      revenueRate: '90%',
      closingRateTarget: '21%',
      closingRateActual: '20%',
      closingRateRate: '95%',
      crmUpdateTarget: '95%',
      crmUpdateActual: '94%',
      crmUpdateRate: '99%',
      totalKpi: '90.3%'
    },
    {
      key: 'NV0000-15',
      name: 'NV F10',
      newCustomersTarget: 23,
      newCustomersActual: 25,
      newCustomersRate: '109%',
      callsTarget: 60,
      callsActual: 62,
      callsRate: '103%',
      quotesTarget: 35,
      quotesActual: 37,
      quotesRate: '106%',
      ordersTarget: 35,
      ordersActual: 37,
      ordersRate: '106%',
      revenueTarget: 500000000,
      revenueActual: 580000000,
      revenueRate: '116%',
      closingRateTarget: '23%',
      closingRateActual: '25%',
      closingRateRate: '109%',
      crmUpdateTarget: '96%',
      crmUpdateActual: '98%',
      crmUpdateRate: '102%',
      totalKpi: '110.06%'
    }
  ]

  const handleFilter = (values: IFilterReportKpiSaleEmployee) => {
    setFilter(values)
  }

  const handleReset = () => {
    console.log('reset')
  }

  return (
    <BaseView>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <BaseTable
        columns={columns}
        data={fakeData}
        total={fakeData.length}
        isLoading={false}
        scroll={{ x: 2000 }}
      />
    </BaseView>
  )
}

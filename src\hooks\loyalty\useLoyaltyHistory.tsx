import { useQuery } from '@tanstack/react-query'
import {
  ILoyaltyHistory,
  ILoyaltyHistoryResponse,
  ELoyaltyMemberRank
} from '~/dto/loyalty-history.dto'

const DUMMY_LOYALTY: ILoyaltyHistoryResponse = {
  data: [
    {
      rewardCode: 'REW001',
      exchangeDate: '2024-03-15',
      customerCode: 'CUS001',
      customerName: 'Nguyễn <PERSON>n <PERSON>',
      phoneNumber: '0909090909',
      programCode: 'PRG001',
      programName: 'Tích điểm đổi quà',
      memberRank: ELoyaltyMemberRank.GOLD,
      totalAccumulatedMoney: 10000000,
      atp: 5000000,
      remainingAtp: 3000000,
      conversionType: 'Đổi quà',
      giftValue: 2000000,
      unit: 'VND',
      usedAmount: 2000000
    },
    {
      rewardCode: 'REW002',
      exchangeDate: '2024-03-14',
      customerCode: 'CUS002',
      customerName: 'Trần Thị B',
      phoneNumber: '0919191919',
      programCode: 'PRG002',
      programName: 'Ưu đãi sinh nhật',
      memberRank: ELoyaltyMemberRank.PLATINUM,
      totalAccumulatedMoney: 15000000,
      atp: 8000000,
      remainingAtp: 6000000,
      conversionType: 'Đổi voucher',
      giftValue: 2000000,
      unit: 'VND',
      usedAmount: 2000000
    },
    {
      rewardCode: 'REW003',
      exchangeDate: '2024-03-13',
      customerCode: 'CUS003',
      customerName: 'Lê Văn C',
      phoneNumber: '0929292929',
      programCode: 'PRG003',
      programName: 'Chào mừng thành viên mới',
      memberRank: ELoyaltyMemberRank.SILVER,
      totalAccumulatedMoney: 5000000,
      atp: 3000000,
      remainingAtp: 2000000,
      conversionType: 'Đổi quà',
      giftValue: 1000000,
      unit: 'VND',
      usedAmount: 1000000
    },
    {
      rewardCode: 'REW004',
      exchangeDate: '2024-03-12',
      customerCode: 'CUS004',
      customerName: 'Phạm Thị D',
      phoneNumber: '0939393939',
      programCode: 'PRG004',
      programName: 'Tích lũy điểm',
      memberRank: ELoyaltyMemberRank.BRONZE,
      totalAccumulatedMoney: 3000000,
      atp: 2000000,
      remainingAtp: 1500000,
      conversionType: 'Đổi voucher',
      giftValue: 500000,
      unit: 'VND',
      usedAmount: 500000
    },
    {
      rewardCode: 'REW005',
      exchangeDate: '2024-03-11',
      customerCode: 'CUS005',
      customerName: 'Hoàng Văn E',
      phoneNumber: '0949494949',
      programCode: 'PRG005',
      programName: 'Hoàn tiền cuối năm',
      memberRank: ELoyaltyMemberRank.GOLD,
      totalAccumulatedMoney: 12000000,
      atp: 6000000,
      remainingAtp: 4000000,
      conversionType: 'Đổi quà',
      giftValue: 2000000,
      unit: 'VND',
      usedAmount: 2000000
    },
    {
      rewardCode: 'REW006',
      exchangeDate: '2024-03-10',
      customerCode: 'CUS006',
      customerName: 'Đỗ Thị F',
      phoneNumber: '0959595959',
      programCode: 'PRG006',
      programName: 'Ưu đãi doanh nghiệp',
      memberRank: ELoyaltyMemberRank.PLATINUM,
      totalAccumulatedMoney: 20000000,
      atp: 10000000,
      remainingAtp: 8000000,
      conversionType: 'Đổi voucher',
      giftValue: 2000000,
      unit: 'VND',
      usedAmount: 2000000
    },
    {
      rewardCode: 'REW007',
      exchangeDate: '2024-03-09',
      customerCode: 'CUS007',
      customerName: 'Vũ Văn G',
      phoneNumber: '0969696969',
      programCode: 'PRG007',
      programName: 'Tặng điểm ngày lễ',
      memberRank: ELoyaltyMemberRank.SILVER,
      totalAccumulatedMoney: 8000000,
      atp: 4000000,
      remainingAtp: 3000000,
      conversionType: 'Đổi quà',
      giftValue: 1000000,
      unit: 'VND',
      usedAmount: 1000000
    },
    {
      rewardCode: 'REW008',
      exchangeDate: '2024-03-08',
      customerCode: 'CUS008',
      customerName: 'Đặng Thị H',
      phoneNumber: '0979797979',
      programCode: 'PRG008',
      programName: 'Giảm giá lần đầu',
      memberRank: ELoyaltyMemberRank.BRONZE,
      totalAccumulatedMoney: 2000000,
      atp: 1000000,
      remainingAtp: 800000,
      conversionType: 'Đổi voucher',
      giftValue: 200000,
      unit: 'VND',
      usedAmount: 200000
    },
    {
      rewardCode: 'REW009',
      exchangeDate: '2024-03-07',
      customerCode: 'CUS009',
      customerName: 'Bùi Văn I',
      phoneNumber: '0989898989',
      programCode: 'PRG009',
      programName: 'Tặng quà cuối năm',
      memberRank: ELoyaltyMemberRank.GOLD,
      totalAccumulatedMoney: 9000000,
      atp: 5000000,
      remainingAtp: 3000000,
      conversionType: 'Đổi quà',
      giftValue: 2000000,
      unit: 'VND',
      usedAmount: 2000000
    },
    {
      rewardCode: 'REW010',
      exchangeDate: '2024-03-06',
      customerCode: 'CUS010',
      customerName: 'Ngô Thị K',
      phoneNumber: '0999999999',
      programCode: 'PRG010',
      programName: 'Tích điểm doanh nghiệp',
      memberRank: ELoyaltyMemberRank.PLATINUM,
      totalAccumulatedMoney: 18000000,
      atp: 9000000,
      remainingAtp: 7000000,
      conversionType: 'Đổi voucher',
      giftValue: 2000000,
      unit: 'VND',
      usedAmount: 2000000
    }
  ],
  total: 10
}

export const useLoyaltyHistory = () => {
  //   const { data, isLoading, refetch } = useQuery({
  //     queryKey: ['loyalty'],
  //     queryFn: () => DUMMY_LOYALTY
  //   })

  return {
    data: DUMMY_LOYALTY,
    isLoading: false,
    refetch: () => {},
    total: DUMMY_LOYALTY.total
  }
}

import {
  EyeOutlined,
  A<PERSON>toreOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  StarOutlined,
  CheckCircleOutlined,
  EnvironmentOutlined,
  BankOutlined,
  UserOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  TagOutlined,
  Bar<PERSON>hartOutlined
} from '@ant-design/icons'
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  Image,
  Empty,
  Divider,
  Badge,
  Tooltip
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom, formatMoneyVND } from '~/common/helper/helper'
import { useModal } from '~/hooks/useModal'
import { ICustomerEvaluation, ITreeNode } from '~/dto/customer-evaluation.dto'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend
} from 'recharts'
import { COLORS } from '~/common/constants'
import { FC } from 'react'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'

interface IAddress {
  id: string
  addressType: string
  market: string
  area: string
  address: string
  areaSize: number
  note: string
  isMain: boolean
  createdBy: string
  createdAt: string
}

type IProps = {
  data: ICustomerEvaluation
}

const { Title } = Typography

// Helper function to render evaluation criteria tree
const renderEvaluationTree = (criteria: ITreeNode[]) => {
  return criteria.map((item, index) => (
    <div key={item.key} style={{ marginBottom: '16px' }}>
      <div
        style={{
          background: '#f8f9fa',
          padding: '12px 16px',
          borderRadius: '8px',
          borderLeft: '4px solid #1890ff',
          marginBottom: '8px'
        }}>
        <BaseText weight='bold' size='md' color='primary'>
          {item.title}
        </BaseText>
      </div>

      {item.children && (
        <div style={{ paddingLeft: '20px' }}>
          {item.children.map((subItem, subIndex) => (
            <div key={subItem.key} style={{ marginBottom: '12px' }}>
              <div
                style={{
                  background: '#fafafa',
                  padding: '8px 12px',
                  borderRadius: '6px',
                  borderLeft: '3px solid #52c41a',
                  marginBottom: '6px'
                }}>
                <BaseText weight='semibold' size='sm'>
                  {subItem.title}
                </BaseText>
              </div>

              {subItem.children && (
                <div style={{ paddingLeft: '16px' }}>
                  <Row gutter={[8, 8]}>
                    {subItem.children.map((leafItem, leafIndex) => (
                      <Col key={leafItem.key}>
                        <Tag
                          color='blue'
                          icon={<CheckCircleOutlined />}
                          style={{
                            margin: '2px',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            fontSize: '12px'
                          }}>
                          {leafItem.title}
                        </Tag>
                      </Col>
                    ))}
                  </Row>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  ))
}

// Helper function to generate sample customer growth data
const generateCustomerGrowthData = (baseCount: number) => {
  const months = [
    'T1',
    'T2',
    'T3',
    'T4',
    'T5',
    'T6',
    'T7',
    'T8',
    'T9',
    'T10',
    'T11',
    'T12'
  ]

  return months.map((month, index) => {
    // Generate realistic growth data with some variation
    const growthRate = 1 + (Math.random() * 0.3 - 0.1) // -10% to +20% variation
    const previousCount =
      index === 0 ? baseCount * 0.7 : baseCount * (0.7 + index * 0.025)
    const currentCount = Math.round(previousCount * growthRate)

    return {
      month,
      customers: currentCount,
      growth: Math.round(((currentCount - previousCount) / previousCount) * 100)
    }
  })
}

// Customer Growth Chart Component
const CustomerGrowthChart = ({ customerCount }: { customerCount: number }) => {
  const data = generateCustomerGrowthData(customerCount)

  return (
    <ResponsiveContainer width='100%' height={300}>
      <BarChart
        data={data}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray='3 3' />
        <XAxis
          dataKey='month'
          tick={{ fontSize: 12 }}
          axisLine={{ stroke: '#d9d9d9' }}
        />
        <YAxis
          tick={{ fontSize: 12 }}
          axisLine={{ stroke: '#d9d9d9' }}
          label={{
            value: 'Số lượng khách hàng',
            angle: -90,
            position: 'insideLeft',
            style: { textAnchor: 'middle' }
          }}
        />
        <RechartsTooltip
          formatter={(value: any, name: any) => [
            `${value} khách hàng`,
            name === 'customers' ? 'Số lượng' : 'Tăng trưởng'
          ]}
          labelFormatter={(label) => `Tháng ${label}`}
        />
        <Legend />
        <Bar
          dataKey='customers'
          fill={COLORS.PRIMARY}
          name='Số lượng khách hàng'
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )
}

export const GeneralInformation: FC<IProps> = ({ data }: IProps) => {
  const {
    name,
    code,
    status,
    customerCount,
    companyName,
    createdBy,
    createdAt,
    source,
    companyCode,
    region,
    address,
    city,
    district,
    ward,
    email,
    phone,
    position,
    firstAccessTime,
    accessCount30Days,
    evaluationCriteria
  } = data

  return (
    <BaseView>
      <div>
        {/* Header Information */}
        <Card
          style={{
            marginBottom: '16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
            border: 'none'
          }}
          bodyStyle={{ padding: '24px' }}>
          <Row gutter={[16, 16]} align='middle'>
            <Col xs={24} sm={16}>
              <Space direction='vertical' size='small'>
                <Title level={2} style={{ color: 'white', margin: 0 }}>
                  {name}
                </Title>
                <Space>
                  <Tag
                    color='white'
                    style={{ color: '#667eea', fontWeight: 'bold' }}>
                    {code}
                  </Tag>
                  <Tag
                    color={status ? 'success' : 'error'}
                    icon={
                      status ? <CheckCircleOutlined /> : <InfoCircleOutlined />
                    }>
                    {status ? 'Hoạt động' : 'Không hoạt động'}
                  </Tag>
                </Space>
              </Space>
            </Col>
            <Col xs={24} sm={8}>
              <Statistic
                title={
                  <span style={{ color: 'white' }}>Số lượng khách hàng</span>
                }
                value={customerCount}
                valueStyle={{
                  color: 'white',
                  fontSize: '32px',
                  fontWeight: 'bold'
                }}
                prefix={<TeamOutlined />}
              />
            </Col>
          </Row>
        </Card>

        <Row gutter={[16, 16]}>
          {/* Company Information */}
          <Col xs={24} lg={12}>
            <Card
              title={
                <Space>
                  <BankOutlined style={{ color: '#1890ff' }} />
                  <BaseText>Thông tin công ty</BaseText>
                </Space>
              }
              size='small'
              style={{ height: '100%' }}>
              <Descriptions column={1} size='small' colon={false}>
                <Descriptions.Item label='Tên công ty'>
                  <BaseText weight='semibold'>{companyName}</BaseText>
                </Descriptions.Item>
                <Descriptions.Item label='Mã công ty'>
                  <Tag color='blue'>{companyCode}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label='Nguồn'>
                  <Tag color='green' icon={<AppstoreOutlined />}>
                    {source}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label='Khu vực'>
                  <Tag color='orange' icon={<EnvironmentOutlined />}>
                    {region}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          {/* Contact Information */}
          <Col xs={24} lg={12}>
            <Card
              title={
                <Space>
                  <UserOutlined style={{ color: '#52c41a' }} />
                  <BaseText>Thông tin liên hệ</BaseText>
                </Space>
              }
              size='small'
              style={{ height: '100%' }}>
              <Descriptions column={1} size='small' colon={false}>
                <Descriptions.Item label='Email'>
                  <BaseText weight='semibold' color='primary'>
                    {email}
                  </BaseText>
                </Descriptions.Item>
                <Descriptions.Item label='Số điện thoại'>
                  <BaseText weight='semibold'>{phone}</BaseText>
                </Descriptions.Item>
                <Descriptions.Item label='Chức vụ'>
                  <Tag color='purple'>{position}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label='Người tạo'>
                  <BaseText>{createdBy}</BaseText>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {/* Address Information */}
        <Card
          title={
            <Space>
              <EnvironmentOutlined style={{ color: '#fa8c16' }} />
              <BaseText>Địa chỉ</BaseText>
            </Space>
          }
          style={{ marginTop: '16px' }}
          size='small'>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <div style={{ textAlign: 'center' }}>
                <BaseText color='textSecondary'>Tỉnh/Thành phố</BaseText>
                <br />
                <BaseText weight='bold'>{city}</BaseText>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ textAlign: 'center' }}>
                <BaseText color='textSecondary'>Quận/Huyện</BaseText>
                <br />
                <BaseText weight='bold'>{district}</BaseText>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ textAlign: 'center' }}>
                <BaseText color='textSecondary'>Phường/Xã</BaseText>
                <br />
                <BaseText weight='bold'>{ward}</BaseText>
              </div>
            </Col>
          </Row>
          <Divider />
          <div
            style={{
              background: '#f8f9fa',
              padding: '12px',
              borderRadius: '6px',
              border: '1px solid #e8e8e8'
            }}>
            <BaseText color='textSecondary'>Địa chỉ chi tiết:</BaseText>
            <br />
            <BaseText weight='semibold'>{address}</BaseText>
          </div>
        </Card>

        {/* Access Information */}
        <Card
          title={
            <Space>
              <CalendarOutlined style={{ color: '#eb2f96' }} />
              <BaseText>Thông tin truy cập</BaseText>
            </Space>
          }
          style={{ marginTop: '16px' }}
          size='small'>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Statistic
                title='Lần truy cập đầu tiên'
                value={formatDateCustom(firstAccessTime, 'DD/MM/YYYY HH:mm')}
                valueStyle={{ color: '#eb2f96', fontSize: '16px' }}
                prefix={<CalendarOutlined />}
              />
            </Col>
            <Col xs={24} sm={12}>
              <Statistic
                title='Số lần truy cập (30 ngày)'
                value={accessCount30Days}
                valueStyle={{ color: '#13c2c2', fontSize: '24px' }}
                prefix={<TeamOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* Evaluation Criteria */}
        <Card
          title={
            <Space>
              <FileTextOutlined style={{ color: '#722ed1' }} />
              <BaseText>Tiêu chí đánh giá</BaseText>
              <Badge
                count={evaluationCriteria?.length || 0}
                style={{ backgroundColor: '#722ed1' }}
              />
            </Space>
          }
          style={{ marginTop: '16px' }}
          size='small'>
          {evaluationCriteria && evaluationCriteria.length > 0 ? (
            <div>
              <BaseText
                color='textSecondary'
                style={{ marginBottom: '16px', display: 'block' }}>
                Các tiêu chí đánh giá được áp dụng cho nhóm khách hàng này:
              </BaseText>
              {renderEvaluationTree(evaluationCriteria)}
            </div>
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <BaseText color='textSecondary'>
                  Chưa có tiêu chí đánh giá
                </BaseText>
              }
              style={{ margin: '20px 0' }}
            />
          )}
        </Card>

        {/* Customer Growth Chart */}
        <Card
          title={
            <Space>
              <BarChartOutlined style={{ color: '#52c41a' }} />
              <BaseText>
                Biểu đồ tăng trưởng số lượng khách hàng theo tháng
              </BaseText>
            </Space>
          }
          style={{ marginTop: '16px' }}
          size='small'>
          <div style={{ padding: '16px 0' }}>
            <CustomerGrowthChart customerCount={customerCount} />
          </div>
          <div
            style={{
              background: '#f8f9fa',
              padding: '12px',
              borderRadius: '6px',
              marginTop: '16px'
            }}>
            <BaseText color='textSecondary' size='sm'>
              📊 Biểu đồ thể hiện sự tăng trưởng số lượng khách hàng theo từng
              tháng trong năm. Dữ liệu được tính toán dựa trên số lượng khách
              hàng hiện tại và xu hướng tăng trưởng.
            </BaseText>
          </div>
        </Card>

        {/* System Information */}
        <Card
          title={
            <Space>
              <InfoCircleOutlined style={{ color: '#13c2c2' }} />
              <BaseText>Thông tin hệ thống</BaseText>
            </Space>
          }
          style={{ marginTop: '16px' }}
          size='small'>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <div style={{ textAlign: 'center' }}>
                <BaseText color='textSecondary'>Ngày tạo</BaseText>
                <br />
                <BaseText weight='bold'>
                  {formatDateCustom(createdAt, 'DD/MM/YYYY HH:mm')}
                </BaseText>
              </div>
            </Col>
            <Col xs={24} sm={12}>
              <div style={{ textAlign: 'center' }}>
                <BaseText color='textSecondary'>Cập nhật lần cuối</BaseText>
                <br />
                <BaseText weight='bold'>
                  {formatDateCustom(createdAt, 'DD/MM/YYYY HH:mm')}
                </BaseText>
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    </BaseView>
  )
}

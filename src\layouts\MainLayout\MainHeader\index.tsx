import { createElement, FC } from 'react'
import { LogoutOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import { Dropdown, Layout, theme as antTheme, Tooltip, Tag, Image, Space } from 'antd'
import { useNavigate } from 'react-router-dom'
import logo from '~/assets/logo/logo.jpg'
import { useThemeStore } from '~/stores/themeStore'
import { useAuthStore } from '~/stores/authStore'
import { useLayoutConfig } from '~/stores/layoutConfig'
import FullScreen from '~/components/FullScreen'
import BaseText from '~/components/BaseText'
import { ReactComponent as MoonSvg } from '~/assets/header/moon.svg'
import { ReactComponent as SunSvg } from '~/assets/header/sun.svg'
import { useLanguageConfig } from '~/i18n/useLanguageConfig'
import i18n from '~/i18n'
import flagVi from '~/assets/icon/icons8-vietnam-100.png'
import flagEn from '~/assets/icon/icons8-american-64.png'

const { Header } = Layout

type IMainHeaderProps = {
  collapsed: boolean
  toggle: () => void
}

type Action = 'userInfo' | 'userSetting' | 'logout'

const MainHeader: FC<IMainHeaderProps> = ({ collapsed, toggle }: IMainHeaderProps) => {
  const token = antTheme.useToken()
  const { themeStyle, changeTheme } = useThemeStore()
  const { logged, logout, userInfo } = useAuthStore()
  const { currentLanguageConfig, languageList } = useLanguageConfig()
  const navigate = useNavigate()

  const { device } = useLayoutConfig()

  const toLogin = () => {
    navigate('/login')
  }

  const onChangeTheme = () => {
    const newTheme = themeStyle === 'dark' ? 'light' : 'dark'

    localStorage.setItem('theme', newTheme)
    changeTheme({ themeStyle: newTheme })
  }

  const toggleLanguage = () => {
    const currentLang = i18n.language
    const currentIndex = languageList.indexOf(currentLang)
    const nextIndex = currentIndex >= 0 ? (currentIndex + 1) % languageList.length : 0
    const nextLang = languageList[nextIndex]
    i18n.changeLanguage(nextLang)
    console.log('Language changed to:', nextLang)
  }

  return (
    <Header
      className='layout-page-header bg-2'
      style={{ backgroundColor: token.token.colorBgContainer }}>
      {device !== 'MOBILE' && (
        <div className='logo' style={{ width: collapsed ? 80 : 200 }}>
          <img src={logo} alt='' style={{ marginRight: collapsed ? '2px' : '20px' }} />
        </div>
      )}
      <div className='layout-page-header-main'>
        <div onClick={toggle}>
          <span id='sidebar-trigger'>
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </span>
        </div>
        <div className='actions'>
          <Space style={{ display: 'flex', alignItems: 'center', textAlign: 'center', justifyContent: 'flex-end' }}>
            <Tooltip title={themeStyle === 'dark' ? 'Sáng' : 'Tối'}>
              <Tag
                style={{
                  cursor: 'pointer',
                  fontSize: '25px',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                {createElement(themeStyle === 'dark' ? SunSvg : MoonSvg, {
                  onClick: onChangeTheme
                })}
              </Tag>
            </Tooltip>
            <Tooltip title={currentLanguageConfig.label}>
              <Tag
                style={{
                  cursor: 'pointer',
                  fontSize: '25px',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onClick={toggleLanguage}>
                <Image
                  src={currentLanguageConfig.label === 'Vietnamese' ? flagVi : flagEn}
                  alt={currentLanguageConfig.icon}
                  style={{
                    width: '40px',
                    height: '40px'
                  }}
                  preview={false}
                />
              </Tag>
            </Tooltip>
            <FullScreen />
            {logged ? (
              <Dropdown                
                menu={{
                  items: [
                    {
                      key: '1',
                      icon: <LogoutOutlined />,
                      label: (
                        <BaseText onClick={logout} style={{ cursor: 'pointer' }}>
                          {'Đăng xuất'}
                        </BaseText>
                      )
                    }
                  ]
                }}>
                <span className='user-action'>
                  <img src={userInfo?.avatar || logo} className='user-avator' alt='avator' />
                </span>
              </Dropdown>
            ) : (
              <BaseText style={{ cursor: 'pointer' }} onClick={toLogin}>
                {'Đăng nhập'}
              </BaseText>
            )}
          </Space>
        </div>
      </div>
    </Header>
  )
}
export default MainHeader

import {
  AppstoreOutlined,
  GiftOutlined,
  HistoryOutlined,
  UnorderedListOutlined,
  WalletOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { GiftScreen } from '../loyalty-program/gift-screen'
import { LoyaltyScreen } from '../loyalty-program/loyalty-screen'
import { HistoryScreen } from '../loyalty-program/history-screen'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const loyaltyChildren = [
  {
    path: 'gift-box',
    title: '<PERSON>h sách quà tặng',
    view: <GiftScreen />,
    icon: <GiftOutlined />,
    isMenu: true
  },
  {
    path: 'loyalty',
    title: 'Quản lý đổi thưởng',
    view: <LoyaltyScreen />,
    icon: <WalletOutlined  />,
    isMenu: true
  },
  {
    path: 'history-loyalty',
    title: 'Lịch sử đổi thưởng',
    view: <HistoryScreen />,
    icon: <HistoryOutlined />,
    isMenu: true
  }
].map((item) =>
  createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu)
)

export default loyaltyChildren

import { useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import { NSCatalog } from '~/common/enums/NSCatalog'
import { IFilter } from '~/components/BaseFilter/BaseFilter'

interface IProps {}
export const useCustomerFilterConfig = (iProps?: IProps) => {
  const handleFilter = (values: any) => {
    //pass values not null and undefined
    const newFilterData = Object.fromEntries(
      Object.entries(values).filter(([, value]) => value != null && value != undefined)
    )
    setFilterData(newFilterData)
  }
  const handleFilterReset = () => {
    setFilterData({})
  }

  const [filterData, setFilterData] = useState<any>({})
  const [filterFields, setFilterFields] = useState<IFilter[]>([
    {
      key: 'code',
      name: 'Mã khách hàng',
      type: enumData.FILTER_TYPE.INPUT.key
    },
    {
      key: 'name',
      name: 'Tên khách hàng',
      type: enumData.FILTER_TYPE.INPUT.key
    },
    //sdt
    {
      key: 'phone',
      name: '<PERSON><PERSON> điện thoại',
      type: enumData.FILTER_TYPE.INPUT.key
    },

    //status
    {
      key: 'isActive',
      name: 'Trạng thái',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: [
        { value: true, name: 'Hoạt động' },
        { value: false, name: 'Không hoạt động' }
      ]
    }
  ])

  return {
    filterFields,
    filterData,
    setFilterData,
    handleFilter,
    handleFilterReset
  }
}

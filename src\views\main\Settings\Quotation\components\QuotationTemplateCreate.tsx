import React, { useState } from 'react'
import {
  Card,
  Form,
  Input,
  Button,
  DatePicker,
  Select,
  Space,
  Table,
  InputNumber,
  Typography,
  Popconfirm,
  Row,
  Col
} from 'antd'
import { PlusOutlined, DeleteOutlined, SaveOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import BaseModal from '~/components/BaseModal'

const { Title } = Typography
const { RangePicker } = DatePicker

interface CreateQuotationModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export const QuotationTemplateCreate = ({
  open,
  onClose,
  onSuccess
}: CreateQuotationModalProps) => {
  const [form] = Form.useForm()
  const [services, setServices] = useState([
    { key: '1', name: '', unit: '', price: 0, vat: '10%' }
  ])
  const [isEditing, setIsEditing] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const handleAddService = () => {
    setServices([
      ...services,
      { key: Date.now().toString(), name: '', unit: '', price: 0, vat: '10%' }
    ])
  }

  const handleRemoveService = (key: string) => {
    setServices(services.filter((s) => s.key !== key))
  }

  const handleServiceChange = (key: string, field: string, value: any) => {
    setServices((prev) =>
      prev.map((s) => (s.key === key ? { ...s, [field]: value } : s))
    )
  }

  const handleSubmit = (values: any) => {
    const payload = {
      ...values,
      services
    }
    console.log('Submitted:', payload)
  }

  const columns = [
    {
      title: 'STT',
      key: 'index',
      render: (_: any, __: any, index: number) => index + 1,
      width: 60
    },
    {
      title: 'Tên dịch vụ',
      dataIndex: 'name',
      render: (_: any, record: any) => (
        <Input
          value={record.name}
          onChange={(e) =>
            handleServiceChange(record.key, 'name', e.target.value)
          }
        />
      )
    },
    {
      title: 'ĐVT',
      dataIndex: 'unit',
      width: 100,
      render: (_: any, record: any) => (
        <Input
          value={record.unit}
          onChange={(e) =>
            handleServiceChange(record.key, 'unit', e.target.value)
          }
        />
      )
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      render: (_: any, record: any) => (
        <InputNumber
          value={record.price}
          onChange={(value) =>
            handleServiceChange(record.key, 'price', value ?? 0)
          }
          style={{ width: '100%' }}
          formatter={(val) => `${val}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
        />
      )
    },
    {
      title: 'VAT',
      dataIndex: 'vat',
      width: 100,
      render: (_: any, record: any) => (
        <Select
          value={record.vat}
          onChange={(value) => handleServiceChange(record.key, 'vat', value)}
          options={[{ value: '0%' }, { value: '5%' }, { value: '10%' }]}
        />
      )
    },
    {
      title: '',
      key: 'actions',
      width: 50,
      render: (_: any, record: any) =>
        services.length > 1 ? (
          <Popconfirm
            title='Xoá dịch vụ này?'
            onConfirm={() => handleRemoveService(record.key)}>
            <Button icon={<DeleteOutlined />} danger size='small' />
          </Popconfirm>
        ) : null
    }
  ]

  const modalContent = (
    <Card>
      <Form layout='vertical' form={form} onFinish={handleSubmit}>
        <Form.Item
          label='Tên template'
          name='templateName'
          rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Form.Item
          label='Mã template'
          name='templateCode'
          rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Form.Item
          label='Đơn vị áp dụng'
          name='applicableUnits'
          rules={[{ required: true }]}>
          <Select mode='multiple' placeholder='Chọn đơn vị'>
            <Select.Option value='ABC Logistics'>ABC Logistics</Select.Option>
            <Select.Option value='Chi nhánh HCM'>Chi nhánh HCM</Select.Option>
            <Select.Option value='Kho HN'>Kho HN</Select.Option>
          </Select>
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Khoảng thời gian hiệu lực' name='dateRange'>
              <RangePicker />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label='Loại dịch vụ'
          name='serviceType'
          rules={[{ required: true }]}>
          <Select>
            <Select.Option value='Vận chuyển nội địa'>
              Vận chuyển nội địa
            </Select.Option>
            <Select.Option value='Kho bãi'>Kho bãi</Select.Option>
            <Select.Option value='Vận chuyển quốc tế'>
              Vận chuyển quốc tế
            </Select.Option>
          </Select>
        </Form.Item>

        <Title level={5}>Danh sách dịch vụ trong báo giá</Title>
        <Table
          dataSource={services}
          columns={columns}
          rowKey='key'
          pagination={false}
          bordered
          size='small'
        />

        <Space style={{ marginTop: 24 }}>
          <Button icon={<PlusOutlined />} onClick={handleAddService}>
            Thêm dòng dịch vụ
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Lưu Template
          </Button>
        </Space>
      </Form>
    </Card>
  )

  return (
    <>
      <BaseModal
        open={open}
        onClose={onClose}
        title='Tạo template báo giá mới'
        description='Thêm template báo giá mới vào hệ thống'
        childrenBody={modalContent}
      />
    </>
  )
}

// Marketing-campaign components

import { DeleteOutlined } from '@ant-design/icons'
import moment from 'moment'
import { useState } from 'react'
import { BaseButton } from '~/components'
import { ICustomerCriteria, ICustomerCriteriaFilter } from '~/dto/customer-criteria.dto'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'
import { Row, Col } from 'antd'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import FilterProduct from './components/FilterProduct'

export const CustomerCriteriaView = () => {
  const handleFilter = (values: any) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      key: '',
      name: '',
      pageIndex: 0,
      pageSize: 10
    })
  }

  const [filter, setFilter] = useState<ICustomerCriteriaFilter>({
    key: '',
    name: '',
    pageIndex: 0,
    pageSize: 10
  })

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  //fakeData
  const [fakeData, setFakeData] = useState<ICustomerCriteria[]>([
    {
      id: 'TC0001',
      key: '1',
      name: 'Ngành nghề',
      description: 'Ngành nghề',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0002',
      key: '2',
      name: 'Thị trường của khách hàng',
      description: 'Thị trường của khách hàng',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0003',
      key: '3',
      name: 'Số lượng nhân sự công ty',
      description: 'Số lượng nhân sự công ty',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0004',
      key: '4',
      name: 'Chủ đầu tư nước ngoài',
      description: 'Chủ đầu tư nước ngoài',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0005',
      key: '5',
      name: 'Châu lục',
      description: 'Châu lục',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0006',
      key: '6',
      name: 'Khu vực',
      description: 'Khu vực',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0007',
      key: '7',
      name: 'Quận/Huyện',
      description: 'Quận/Huyện',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0008',
      key: '8',
      name: 'Phường/Xã',
      description: 'Phường/Xã',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC0009',
      key: '9',
      name: 'Doanh số',
      description: 'Doanh số',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: 'TC00010',
      key: '10',
      name: 'Công nợ',
      description: 'Công nợ',
      status: 'Hoạt động',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    }
  ])

  const handleDelete = (record: any) => {
    setFakeData(fakeData.filter((item) => item.id !== record.id))
  }

  const columns: any[] = [
    {
      title: 'Mã',
      align: 'center',
      dataIndex: 'id',
      key: 'id',
      width: 50
    },
    {
      title: 'Tên',
      align: 'center',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: 'Mô tả',
      align: 'center',
      dataIndex: 'description',
      key: 'description',
      width: 200
    },
    {
      title: 'Trạng thái',
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      width: 100
    },
    {
      title: 'Ngày tạo',
      align: 'center',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Ngày cập nhật',
      align: 'center',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Chức năng',
      align: 'center',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={fakeData}
            total={0}
            isLoading={false}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

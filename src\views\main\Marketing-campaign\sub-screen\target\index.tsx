import React, { useState } from 'react'
import { Table, Input, Select, Button, Space, Card, Tag } from 'antd'
import {
  EditOutlined,
  SearchOutlined,
  WarningOutlined
} from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { getColorStatus } from '~/common/utils/common.utils'

const { Option } = Select

const mockData = [
  {
    key: '1',
    serialNo: 1,
    groupCode: '1143',
    groupName: 'KH_NGÀY 06.06.2025.ALL DATA',
    customerCount: 0,
    externalImportedCount: 44418,
    unidentifiedCustomerCount: 0,
    status: 'active',
    createdBy: 'trant',
    createdAt: '06/06/2025 10:51:43 AM'
  },
  {
    key: '2',
    serialNo: 2,
    groupCode: '1142',
    groupName: 'KH_NGÀY 07.05.2025.ALL DATA',
    customerCount: 0,
    externalImportedCount: 45544,
    unidentifiedCustomerCount: 0,
    status: 'active',
    createdBy: 'trant',
    createdAt: '07/05/2025 11:28:35 AM'
  },
  {
    key: '3',
    serialNo: 3,
    groupCode: '1141',
    groupName: 'KH_NGÀY 05.05.2025.ALL DATA',
    customerCount: 12,
    externalImportedCount: 32000,
    unidentifiedCustomerCount: 3,
    status: 'inactive',
    createdBy: 'linhnt',
    createdAt: '05/05/2025 09:15:20 AM'
  },
  {
    key: '4',
    serialNo: 4,
    groupCode: '1140',
    groupName: 'KH_NGÀY 01.05.2025.ALL DATA',
    customerCount: 5,
    externalImportedCount: 15000,
    unidentifiedCustomerCount: 1,
    status: 'active',
    createdBy: 'minht',
    createdAt: '01/05/2025 08:10:05 AM'
  },
  {
    key: '5',
    serialNo: 5,
    groupCode: '1139',
    groupName: 'KH_NGÀY 28.04.2025.ALL DATA',
    customerCount: 0,
    externalImportedCount: 28900,
    unidentifiedCustomerCount: 0,
    status: 'inactive',
    createdBy: 'hoangd',
    createdAt: '28/04/2025 04:45:50 PM'
  },
  {
    key: '6',
    serialNo: 6,
    groupCode: '1138',
    groupName: 'KH_NGÀY 25.04.2025.ALL DATA',
    customerCount: 3,
    externalImportedCount: 17800,
    unidentifiedCustomerCount: 2,
    status: 'active',
    createdBy: 'trant',
    createdAt: '25/04/2025 02:30:00 PM'
  },
  {
    key: '7',
    serialNo: 7,
    groupCode: '1137',
    groupName: 'KH_NGÀY 22.04.2025.ALL DATA',
    customerCount: 0,
    externalImportedCount: 22000,
    unidentifiedCustomerCount: 0,
    status: 'inactive',
    createdBy: 'trant',
    createdAt: '22/04/2025 01:15:00 PM'
  },
  {
    key: '8',
    serialNo: 8,
    groupCode: '1136',
    groupName: 'KH_NGÀY 20.04.2025.ALL DATA',
    customerCount: 7,
    externalImportedCount: 35000,
    unidentifiedCustomerCount: 1,
    status: 'active',
    createdBy: 'linhnt',
    createdAt: '20/04/2025 10:00:00 AM'
  },
  {
    key: '9',
    serialNo: 9,
    groupCode: '1135',
    groupName: 'KH_NGÀY 18.04.2025.ALL DATA',
    customerCount: 10,
    externalImportedCount: 41000,
    unidentifiedCustomerCount: 5,
    status: 'inactive',
    createdBy: 'hoangd',
    createdAt: '18/04/2025 11:45:00 AM'
  },
  {
    key: '10',
    serialNo: 10,
    groupCode: '1134',
    groupName: 'KH_NGÀY 15.04.2025.ALL DATA',
    customerCount: 8,
    externalImportedCount: 30000,
    unidentifiedCustomerCount: 0,
    status: 'active',
    createdBy: 'minht',
    createdAt: '15/04/2025 03:30:00 PM'
  }
]

export const TargetView = () => {
  const navigate = useNavigate()
  const [filters, setFilters] = useState({
    groupCode: '',
    groupName: '',
    status: ''
  })
  const { t } = useTranslation()

  const handleInputChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleEditTarget = (id: string) => {
    navigate(`edit?id=${id}`)
  }

  const statusContent = (status: string) => {
    switch (status) {
      case 'active':
        return 'Hoạt động'
      case 'inactive':
        return 'Không hoạt động'
      default:
        return 'Không xác định'
    }
  }

  const filteredData = mockData.filter(
    (item) =>
      (!filters.groupCode || item.groupCode.includes(filters.groupCode)) &&
      (!filters.groupName ||
        item.groupName
          .toLowerCase()
          .includes(filters.groupName.toLowerCase())) &&
      (!filters.status || item.status === filters.status)
  )

  const columns = [
    {
      title: t('target.header.itemLine'),
      dataIndex: 'serialNo',
      key: 'serialNo',
      width: 60
    },
    {
      title: t('target.header.groupCode'),
      dataIndex: 'groupCode',
      key: 'groupCode'
    },
    {
      title: t('target.header.groupName'),
      dataIndex: 'groupName',
      key: 'groupName'
    },
    {
      title: t('target.header.customerCount'),
      dataIndex: 'customerCount',
      key: 'customerCount'
    },
    {
      title: t('target.header.unidentifiedCustomerCount'),
      dataIndex: 'unidentifiedCustomerCount',
      key: 'unidentifiedCustomerCount'
    },
    {
      title: t('target.header.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag
          color={getColorStatus(status)}
          style={{ fontSize: '14px', padding: '4px 12px' }}>
          {statusContent(status)}
        </Tag>
      )
    },
    {
      title: t('target.header.createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy'
    },
    {
      title: t('target.header.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: t('target.header.actions'),
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type='default'
            icon={<EditOutlined />}
            onClick={() => handleEditTarget('21321321321')}>
            {t('button.title.edit')}
          </Button>
          <Button type='default' icon={<WarningOutlined />} danger>
            {t('button.title.inActive')}
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Card title={t('target.title')}>
        <Space style={{ marginBottom: 16 }} wrap>
          <Input
            addonBefore={t('target.header.groupCode')}
            placeholder={t('target.place_holder.groupCode')}
            value={filters.groupCode}
            onChange={(e) => handleInputChange('groupCode', e.target.value)}
          />
          <Input
            addonBefore={t('target.header.groupName')}
            placeholder={t('target.place_holder.groupName')}
            value={filters.groupName}
            onChange={(e) => handleInputChange('groupName', e.target.value)}
          />
          <Select
            placeholder={t('target.place_holder.status')}
            style={{ width: 160 }}
            value={filters.status}
            onChange={(value) => handleInputChange('status', value)}
            allowClear>
            <Option value=''>--{t('target.header.status')}--</Option>
            <Option value='active'>Active</Option>
            <Option value='inactive'>Inactive</Option>
          </Select>
          <Button type='primary' icon={<SearchOutlined />}>
            {t('button.title.search')}
          </Button>
        </Space>

        <BaseTable
          columns={columns}
          data={filteredData}
          total={filteredData.length}
          isLoading={false}
          pagination={{ pageSize: 10 }}
          rowKey='key'
        />
      </Card>
    </div>
  )
}

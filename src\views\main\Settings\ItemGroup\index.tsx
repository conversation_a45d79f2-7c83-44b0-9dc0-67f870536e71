import React, { useState } from 'react'
import { Table, Tag, Button, Space, Card, Select, Input, Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { DeleteOutlined, EyeOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'

export interface Invoice {
  key: string
  code: string
  customerName: string
  // Đơn vị phát hành
  issuingUnit: 'MISA' | 'VNPT Invoice'
  serviceType: 'Logistic' | 'Khác'
  status: 'active' | 'inactive'
  createdAt: string
  createdBy: string
}

const { Option } = Select

export const CatalogGroupView = () => {
  const [searchCode, setSearchCode] = useState('')
  const [searchName, setSearchName] = useState('')
  const [status, setStatus] = useState('')

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_text, _record, index) => index + 1
    },
    {
      title: 'Mã nhóm sản phẩm',
      dataIndex: 'code',
      key: 'code'
    },

    {
      title: 'Tên nhóm sản phẩm',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description'
    },

    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: Invoice['status']) => {
        const color = status === 'active' ? 'green' : 'red'
        const label = status === 'active' ? 'Đang hoạt động' : 'Ngưng hoạt động'

        return <Tag color={color}>{label}</Tag>
      }
    },

    {
      title: 'Tác vụ',
      key: 'action',
      render: (_, record) => (
        <Space>
          <DetailButton data={record} />
          <EditButton data={record} />

          <Button
            icon={<DeleteOutlined />}
            danger
            type='primary'
            onClick={() => console.log('Xóa', record.code)}></Button>
        </Space>
      )
    }
  ]

  return (
    <BaseView>
      <Card title='Quản lý nhóm sản phẩm'>
        {/* Bộ lọc */}
        <Row style={{ marginBottom: 16 }} gutter={24}>
          <Col span={6}>
            <div>Loại sản phẩm: </div>
            <Select
              style={{ width: '100%' }}
              placeholder='Chọn loại sản phẩm'
              allowClear
              value={status}
              onChange={setStatus}>
              <Option value=''>--Chọn loại sản phẩm--</Option>
              <Option value='active'>Loại sản phẩm 1</Option>
              <Option value='inactive'>Loại sản phẩm 2</Option>
            </Select>
          </Col>
          <Col span={6}>
            <div>Mã nhóm sản phẩm: </div>
            <Input
              placeholder='Nhập mã nhóm sản phẩm'
              value={searchCode}
              onChange={(e) => setSearchCode(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <div>Tên nhóm sản phẩm: </div>
            <Input
              placeholder='Nhập tên nhóm sản phẩm'
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <div>Trạng thái: </div>
            <Select
              style={{ width: '100%' }}
              placeholder='Chọn trạng thái'
              allowClear
              value={status}
              onChange={setStatus}>
              <Option value=''>--Chọn trạng thái--</Option>
              <Option value='active'>Active</Option>
              <Option value='inactive'>Inactive</Option>
            </Select>
          </Col>
          <Col span={24} style={{ marginTop: 10, textAlign: 'center' }}>
            <Button type='primary' icon={<SearchOutlined />} style={{ marginRight: 10 }}>
              Tìm Kiếm
            </Button>
            <Button type='default' icon={<ReloadOutlined />}>
              Làm mới
            </Button>
          </Col>
        </Row>
        <BaseTable columns={columns} data={[]} total={0} isLoading={false} rowKey='key' />
      </Card>
    </BaseView>
  )
}

import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Upload,
  message,
  Card,
  DatePicker
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { EProduct } from '~/common/enums/NSProduct'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import moment from 'moment'
import { useModal } from '~/hooks/useModal'

const { Option } = Select
const { TextArea } = Input

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateEmployeeModal = ({
  open,
  onClose,
  onSuccess
}: CreateProductModalProps) => {
  const [form] = useForm()
  useEffect(() => {
    form.setFieldsValue({
      employeeCode: null,
      name: null,
      email: null,
      phone: null,
      department: null,
      position: null,
      dateOfBirth: null,
      gender: null,
      address: null,
      note: null
    })
  }, [open])

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form layout='vertical' form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Họ và tên' name='name'>
                <Input placeholder='Nhập họ và tên' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Email' name='email'>
                <Input placeholder='Nhập email' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Số điện thoại' name='phone'>
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Phòng ban' name='department'>
                <Input placeholder='Nhập phòng ban' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Chức vụ' name='position'>
                <Input placeholder='Nhập chức vụ' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Ngày sinh' name='dateOfBirth'>
                <DatePicker
                  format='DD/MM/YYYY'
                  placeholder='Chọn ngày sinh'
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Giới tính' name='gender'>
                <Select placeholder='Chọn giới tính'>
                  <Option value='male'>Nam</Option>
                  <Option value='female'>Nữ</Option>
                  <Option value='other'>Khác</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Địa chỉ' name='address'>
                <Input placeholder='Nhập địa chỉ' />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label='Ghi chú' name='note'>
                <TextArea rows={4} placeholder='Nhập ghi chú' />
              </Form.Item>
            </Col>

            <Col span={24} style={{ textAlign: 'right' }}>
              <Button type='default' style={{ marginRight: 8 }}>
                Lưu
              </Button>
              <Button type='primary'>Lưu và tiếp tục cập nhật</Button>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo nhân viên'
      description='Thêm nhân viên mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateEmployeeModal

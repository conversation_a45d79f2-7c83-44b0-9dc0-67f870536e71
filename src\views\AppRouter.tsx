import { FC } from 'react'
import { useNavigate, useRoutes } from 'react-router'
import { RouteObject } from 'react-router'
import { navService, toastService } from '~/services'
import LoginView from './auth/LoginView'
import { MainRouter } from './main/main.router'
import MainLayout from '~/layouts/MainLayout'
import ApeCallback from './auth/LoginView/ApeCallback'

const appRouter: RouteObject[] = [
  {
    path: 'login',
    element: <LoginView />
  },
  {
    path: '/ape-callback',
    element: <ApeCallback />
  },
  {
    path: '/',
    element: <MainLayout />,
    children: MainRouter() as RouteObject[]
  }
]
const AppRouter: FC = ({ ...props }) => {
  const navigate = useNavigate()
  navService.initRefNav(navigate)
  toastService.initNavigation(navigate)
  const element = useRoutes(appRouter)
  return element
}

export default AppRouter

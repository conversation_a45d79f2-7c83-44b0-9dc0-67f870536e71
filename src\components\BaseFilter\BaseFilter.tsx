import { <PERSON><PERSON>, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { enumData } from '~/common/enums/enumData'

interface ISelectOption {
  value: any
  name: string
}

export interface IFilter {
  key: string
  name: string
  type: string
  selectOptions?: ISelectOption[]
}
interface IProps {
  onFilter: (values: any) => void
  onReset: () => void
  isLoading: boolean
  filters: IFilter[]
}

const BaseFilter: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading, filters } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            {filters.map((filter) => {
              switch (filter.type) {
                case enumData.FILTER_TYPE.INPUT.key:
                  return (
                    <Col span={6}>
                      <Form.Item label={filter.name + ' :'} name={filter.key}>
                        <Input placeholder={`Nhập ${filter.name}`} />
                      </Form.Item>
                    </Col>
                  )
                case enumData.FILTER_TYPE.SELECT.key:
                  return (
                    <Col span={6}>
                      <Form.Item label={filter.name + ' :'} name={filter.key}>
                        <Select placeholder={`Chọn ${filter.name}`} allowClear>
                          {filter.selectOptions?.map((item) => (
                            <Select.Option key={item.value}>{item.name}</Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  )

                case enumData.FILTER_TYPE.SELECT_SEARCH.key:
                  return (
                    <Col span={6}>
                      <Form.Item label={filter.name + ' :'} name={filter.key}>
                        <Select placeholder={`Chọn ${filter.name}`} allowClear showSearch>
                          {filter.selectOptions?.map((item) => (
                            <Select.Option key={item.value} value={item.name}>
                              {item.name}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  )

                case enumData.FILTER_TYPE.DATE.key:
                  return (
                    <Col span={6}>
                      <Form.Item label={filter.name + ' :'} name={filter.key}>
                        <DatePicker
                          placeholder={`Chọn ${filter.name}`}
                          format={'YYYY-MM-DD'}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  )

                //date range
                case enumData.FILTER_TYPE.DATE_RANGE.key:
                  return (
                    <Col span={6}>
                      <Form.Item label={filter.name + ' :'} name={filter.key}>
                        <DatePicker.RangePicker
                          placeholder={['Từ ngày', 'Đến ngày']}
                          format={'YYYY-MM-DD'}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  )

                default:
                  break
              }
            })}
          </Row>

          <Row gutter={24} style={{ marginTop: 10 }}>
            <Form.Item style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  gap: 10,
                  justifyContent: 'center'
                }}>
                <Button
                  type='primary'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleFilter}
                  loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button
                  type='default'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleReset}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </div>
            </Form.Item>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default BaseFilter

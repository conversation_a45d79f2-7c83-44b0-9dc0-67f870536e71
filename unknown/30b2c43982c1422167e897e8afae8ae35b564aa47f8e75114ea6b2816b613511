import { FC } from 'react'
import { Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface IManager {
  id: string
  employeeCode: string
  salesPerson: string
  department: string
  company: string
  createdBy: string
  createdAt: string
}

type IProps = {}

const dummyData: IManager[] = [
  {
    id: '1',
    employeeCode: 'NV001',
    salesPerson: 'Nguyễn Văn A',
    department: 'Phòng Kinh doanh',
    company: 'Công ty TNHH ABC',
    createdBy: 'Admin',
    createdAt: '2024-03-20T08:00:00'
  },
  {
    id: '2',
    employeeCode: 'NV002',
    salesPerson: 'Trần Thị B',
    department: 'Phòng Kinh doanh',
    company: 'Công ty TNHH XYZ',
    createdBy: 'Manager',
    createdAt: '2024-03-19T09:30:00'
  },
  {
    id: '3',
    employeeCode: 'NV003',
    salesPerson: 'Lê Văn <PERSON>',
    department: 'Phòng Kinh doanh <PERSON>',
    company: 'Công ty TNHH DEF',
    createdBy: 'Admin',
    createdAt: '2024-03-18T10:15:00'
  },
  {
    id: '4',
    employeeCode: 'NV004',
    salesPerson: 'Phạm Thị D',
    department: 'Phòng Kinh doanh Miền Trung',
    company: 'Công ty TNHH GHI',
    createdBy: 'Manager',
    createdAt: '2024-03-17T14:20:00'
  },
  {
    id: '5',
    employeeCode: 'NV005',
    salesPerson: 'Hoàng Văn E',
    department: 'Phòng Kinh doanh Miền Nam',
    company: 'Công ty TNHH JKL',
    createdBy: 'Admin',
    createdAt: '2024-03-16T16:45:00'
  }
]

export const ManagerTab: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  const columns: ColumnsType<IManager> = [
    {
      title: t('customer:customer.customer_detail.manager_tab.columns.stt'),
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: t(
        'customer:customer.customer_detail.manager_tab.columns.employeeCode'
      ),
      dataIndex: 'employeeCode',
      key: 'employeeCode',
      width: 150
    },
    {
      title: t(
        'customer:customer.customer_detail.manager_tab.columns.salesPerson'
      ),
      dataIndex: 'salesPerson',
      key: 'salesPerson',
      width: 200
    },
    {
      title: t(
        'customer:customer.customer_detail.manager_tab.columns.department'
      ),
      dataIndex: 'department',
      key: 'department',
      width: 200
    },
    {
      title: t('customer:customer.customer_detail.manager_tab.columns.company'),
      dataIndex: 'company',
      key: 'company',
      width: 250
    },
    {
      title: t(
        'customer:customer.customer_detail.manager_tab.columns.createdBy'
      ),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150
    },
    {
      title: t(
        'customer:customer.customer_detail.manager_tab.columns.createdAt'
      ),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (value: string) =>
        value ? new Date(value).toLocaleDateString('vi-VN') : '-'
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={dummyData}
            total={dummyData.length}
            isLoading={false}
            scroll={{ x: 1200 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

import { EditOutlined, SaveOutlined } from '@ant-design/icons'
import {
  Card,
  Tag,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  DatePicker,
  message
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import BaseModal from '~/components/BaseModal'
import { useEffect } from 'react'
import { useForm } from 'antd/es/form/Form'
import { IGift } from '~/dto/gift.dto'
import dayjs from 'dayjs'
import { useModal } from '~/hooks/useModal'
import { useTranslation } from 'react-i18next'

const { Option } = Select

interface EditButtonProps {
  data: IGift
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()
  const { t } = useTranslation()

  const {
    programName,
    memberLevel,
    conversionType,
    giftValue,
    unit,
    startDate,
    endDate,
    status
  } = data || {}

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        programName,
        memberLevel,
        conversionType,
        giftValue,
        unit,
        startDate: startDate ? dayjs(startDate) : undefined,
        endDate: endDate ? dayjs(endDate) : undefined,
        status
      })
    }
  }, [open, data, form])

  if (!data) return null

  const handleSave = async (values: any) => {
    if (!data) return
    const body = {
      ...values,
      id: data?.id,
      startDate: values.startDate?.toISOString(),
      endDate: values.endDate?.toISOString()
    }

    try {
      // TODO: Implement update loyalty mutation
      // await updateLoyalty(body)
      closeModal()
      onSuccess && onSuccess()
      form.resetFields()
    } catch (error) {
      message.error(t('customer:customer.customer_edit.validation.updateError'))
    }
  }

  // Header thông tin chương trình
  const loyaltyHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>
              {t('customer:customer.customer_edit.fields.fullName')}:
            </BaseText>
            <br />
            <BaseText weight='bold'>{programName || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>
              {t('customer:customer.customer_edit.fields.customerRank')}:
            </BaseText>
            <br />
            <BaseText weight='bold'>{memberLevel || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>
              {t('customer:customer.customer_edit.fields.status')}:
            </BaseText>
            <br />
            <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
              {status === 'ACTIVE'
                ? t('customer:customer.customer_edit.options.status.active')
                : t('customer:customer.customer_edit.options.status.inactive')}
            </Tag>
          </div>
        </Col>
      </Row>
    </Card>
  )

  const modalContent = (
    <div>
      {loyaltyHeader}

      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.fullName')}
              name='programName'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.required',
                    {
                      field: t(
                        'customer:customer.customer_edit.fields.fullName'
                      )
                    }
                  )
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer.customer_edit.placeholders.fullName'
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.customerRank')}
              name='memberLevel'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.selectRequired',
                    {
                      field: t(
                        'customer:customer.customer_edit.fields.customerRank'
                      )
                    }
                  )
                }
              ]}>
              <Select
                placeholder={t(
                  'customer:customer.customer_edit.placeholders.selectRank'
                )}>
                <Option value='BRONZE'>
                  {t('customer:customer.customer_edit.options.customerRank.A')}
                </Option>
                <Option value='SILVER'>
                  {t('customer:customer.customer_edit.options.customerRank.B')}
                </Option>
                <Option value='GOLD'>
                  {t('customer:customer.customer_edit.options.customerRank.C')}
                </Option>
                <Option value='PLATINUM'>
                  {t('customer:customer.customer_edit.options.customerRank.D')}
                </Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.customerSource')}
              name='conversionType'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.selectRequired',
                    {
                      field: t(
                        'customer:customer.customer_edit.fields.customerSource'
                      )
                    }
                  )
                }
              ]}>
              <Select
                placeholder={t(
                  'customer:customer.customer_edit.placeholders.selectSource'
                )}>
                <Option value='POINT'>
                  {t(
                    'customer:customer.customer_edit.options.customerSource.website'
                  )}
                </Option>
                <Option value='MONEY'>
                  {t(
                    'customer:customer.customer_edit.options.customerSource.facebook'
                  )}
                </Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.signboardCount')}
              name='giftValue'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.required',
                    {
                      field: t(
                        'customer:customer.customer_edit.fields.signboardCount'
                      )
                    }
                  )
                }
              ]}>
              <InputNumber
                style={{ width: '100%' }}
                placeholder={t(
                  'customer:customer.customer_edit.placeholders.signboardCount'
                )}
                min={0}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.unit')}
              name='unit'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.required',
                    { field: t('customer:customer.customer_edit.fields.unit') }
                  )
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer.customer_edit.placeholders.unit'
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.status')}
              name='status'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.selectRequired',
                    {
                      field: t('customer:customer.customer_edit.fields.status')
                    }
                  )
                }
              ]}>
              <Select
                placeholder={t(
                  'customer:customer.customer_edit.placeholders.selectStatus'
                )}>
                <Option value='ACTIVE'>
                  {t('customer:customer.customer_edit.options.status.active')}
                </Option>
                <Option value='INACTIVE'>
                  {t('customer:customer.customer_edit.options.status.inactive')}
                </Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.startDate')}
              name='startDate'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.selectRequired',
                    {
                      field: t(
                        'customer:customer.customer_edit.fields.startDate'
                      )
                    }
                  )
                }
              ]}>
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer.customer_edit.fields.endDate')}
              name='endDate'
              rules={[
                {
                  required: true,
                  message: t(
                    'customer:customer.customer_edit.validation.selectRequired',
                    {
                      field: t('customer:customer.customer_edit.fields.endDate')
                    }
                  )
                }
              ]}>
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            {t('customer:customer.customer_edit.cancel')}
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            {t('customer:customer.customer_edit.update')}
          </Button>
        </div>
      </Form>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        tooltip={t('customer:customer.edit')}
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title={t('customer:customer.customer_edit.title')}
        description={t('customer:customer.customer_edit.description')}
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

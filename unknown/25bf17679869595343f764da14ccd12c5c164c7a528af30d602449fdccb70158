import { useState, FC } from 'react'
import { Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useLoyaltyHistory } from '~/hooks/loyalty/useLoyaltyHistory'
import { formatMoneyVND } from '~/common/helper/helper'
import {
  IFilterLoyaltyHistory,
  ILoyaltyHistory
} from '~/dto/loyalty-history.dto'
import FilterLoyaltyHistory from './components-loyalty-history/FilterLoyaltyHistory'

type IProps = {}

export const HistoryScreen: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<IFilterLoyaltyHistory>({
    customerCode: '',
    customerName: '',
    phoneNumber: '',
    programCode: '',
    programName: '',
    memberRank: '',
    pageIndex: 1,
    pageSize: 10
  })

  const { data, isLoading } = useLoyaltyHistory()

  const handleFilter = (values: IFilterLoyaltyHistory) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      customerCode: '',
      customerName: '',
      phoneNumber: '',
      programCode: '',
      programName: '',
      memberRank: '',
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const columns: ColumnsType<ILoyaltyHistory> = [
    {
      title: t('loyalty:history_screen.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      fixed: 'left',
      render: (_, __, index) => index + 1
    },
    {
      title: t('loyalty:history_screen.columns.rewardCode'),
      dataIndex: 'rewardCode',
      key: 'rewardCode',
      width: 120,
      align: 'center',
      fixed: 'left'
    },
    {
      title: t('loyalty:history_screen.columns.exchangeDate'),
      dataIndex: 'exchangeDate',
      key: 'exchangeDate',
      width: 150,
      align: 'center',
      fixed: 'left',
      render: (value: string) =>
        value ? new Date(value).toLocaleDateString('vi-VN') : '-'
    },
    {
      title: t('loyalty:history_screen.columns.customerCode'),
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      align: 'center',
      fixed: 'left'
    },
    {
      title: t('loyalty:history_screen.columns.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.phoneNumber'),
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      width: 120,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.programCode'),
      dataIndex: 'programCode',
      key: 'programCode',
      width: 120,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.programName'),
      dataIndex: 'programName',
      key: 'programName',
      width: 200,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.memberRank'),
      dataIndex: 'memberRank',
      key: 'memberRank',
      width: 120,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.totalAccumulatedMoney'),
      dataIndex: 'totalAccumulatedMoney',
      key: 'totalAccumulatedMoney',
      width: 150,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:history_screen.columns.atp'),
      dataIndex: 'atp',
      key: 'atp',
      width: 120,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:history_screen.columns.remainingAtp'),
      dataIndex: 'remainingAtp',
      key: 'remainingAtp',
      width: 120,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:history_screen.columns.conversionType'),
      dataIndex: 'conversionType',
      key: 'conversionType',
      width: 150,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.giftValue'),
      dataIndex: 'giftValue',
      key: 'giftValue',
      width: 150,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:history_screen.columns.unit'),
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      align: 'center'
    },
    {
      title: t('loyalty:history_screen.columns.usedAmount'),
      dataIndex: 'usedAmount',
      key: 'usedAmount',
      width: 120,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterLoyaltyHistory
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={isLoading}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={data?.total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 2500 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

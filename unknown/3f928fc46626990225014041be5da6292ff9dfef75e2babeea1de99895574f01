import { FC } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface IDocument {
  id: string
  fileType: string
  fileName: string
  createdBy: string
  createdAt: string
}

interface IFilterDocument {
  fileType?: string
  fileName?: string
  createdBy?: string
  pageIndex: number
  pageSize: number
}

type IProps = {}

// Mock data for demonstration
const mockDocuments: IDocument[] = [
  {
    id: '1',
    fileType: 'PDF',
    fileName: 'Hợp đồng mua bán số 123.pdf',
    createdBy: '<PERSON><PERSON><PERSON>n Văn <PERSON>',
    createdAt: '2024-03-15T08:30:00'
  },
  {
    id: '2',
    fileType: 'DOCX',
    fileName: 'Báo cáo tài chính Q1.docx',
    createdBy: 'Trần Thị B',
    createdAt: '2024-03-14T14:20:00'
  },
  {
    id: '3',
    fileType: 'XLSX',
    fileName: 'Danh sách khách hàng.xlsx',
    createdBy: 'Lê Văn C',
    createdAt: '2024-03-13T10:15:00'
  },
  {
    id: '4',
    fileType: 'PDF',
    fileName: 'Biên bản họp.pdf',
    createdBy: 'Phạm Thị D',
    createdAt: '2024-03-12T16:45:00'
  },
  {
    id: '5',
    fileType: 'JPG',
    fileName: 'Hình ảnh sản phẩm.jpg',
    createdBy: 'Hoàng Văn E',
    createdAt: '2024-03-11T09:30:00'
  }
]

export const DocumentTab: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  // Using mock data instead of API call
  const data = { data: mockDocuments, total: mockDocuments.length }
  const isLoading = false

  const columns: ColumnsType<IDocument> = [
    {
      title: t('customer:customer.customer_detail.document_tab.columns.stt'),
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: t(
        'customer:customer.customer_detail.document_tab.columns.fileType'
      ),
      dataIndex: 'fileType',
      key: 'fileType',
      width: 150,
      render: (value: string) => {
        const colorMap: Record<string, string> = {
          PDF: 'red',
          DOCX: 'blue',
          XLSX: 'green',
          JPG: 'orange'
        }
        return (
          <Tag
            color={colorMap[value] || 'default'}
            style={{ fontSize: '14px', padding: '4px 12px' }}>
            {value?.toUpperCase() || '-'}
          </Tag>
        )
      }
    },
    {
      title: t(
        'customer:customer.customer_detail.document_tab.columns.fileName'
      ),
      dataIndex: 'fileName',
      key: 'fileName',
      width: 300
    },
    {
      title: t(
        'customer:customer.customer_detail.document_tab.columns.createdBy'
      ),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 200
    },
    {
      title: t(
        'customer:customer.customer_detail.document_tab.columns.createdAt'
      ),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) =>
        value ? new Date(value).toLocaleDateString('vi-VN') : '-'
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={data?.total || 0}
            isLoading={isLoading}
            scroll={{ x: 1000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

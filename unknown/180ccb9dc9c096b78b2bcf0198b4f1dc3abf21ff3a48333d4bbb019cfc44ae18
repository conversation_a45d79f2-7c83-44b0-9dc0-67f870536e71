import { IContactEvaluationResponse } from '~/dto/contact-evaluation.dto'

const DUMMY_DATA: IContactEvaluationResponse = {
  data: [
    {
      id: '1',
      code: 'PK001',
      name: '<PERSON><PERSON><PERSON><PERSON> hàng VIP',
      customerCount: 150,
      status: true,
      companyName: 'Công ty TNHH ABC',
      createdBy: '<PERSON><PERSON><PERSON><PERSON>',
      createdAt: '2024-01-15T08:00:00Z',
      source: 'Website',
      region: 'Miền Nam',
      address: '123 Đường Nguyễn Huệ',
      city: 'TP. Hồ Chí Minh',
      district: 'Quận 1',
      ward: '<PERSON><PERSON>ờng Bến Ngh<PERSON>',
      email: '<EMAIL>',
      phone: '0123456789',
      position: '<PERSON><PERSON><PERSON><PERSON> đốc',
      firstAccessTime: '2024-03-20T08:00:00Z',
      accessCount30Days: 15,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. <PERSON>uy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: '<PERSON><PERSON><PERSON> nghề',
              value: '1-1',
              children: [
                { key: '1-1-1', title: '<PERSON><PERSON><PERSON>u<PERSON>', value: '1-1-1' },
                { key: '1-1-2', title: 'Th<PERSON>ơng mại', value: '1-1-2' }
              ]
            },
            {
              key: '1-3',
              title: 'Số l<PERSON><PERSON>ng nhân sự công ty',
              value: '1-3',
              children: [
                { key: '1-3-5', title: '501-1000', value: '1-3-5' },
                { key: '1-3-6', title: 'Trên 1000', value: '1-3-6' }
              ]
            }
          ]
        },
        {
          key: '3',
          title: '3. Tài chính khách hàng',
          value: '3',
          children: [
            { key: '3-1', title: 'Doanh số', value: '3-1' },
            {
              key: '3-4',
              title: 'Doanh thu trung bình trên 1 đơn hàng',
              value: '3-4'
            }
          ]
        }
      ]
    },
    {
      id: '2',
      code: 'PK002',
      name: 'Khách hàng Thường',
      customerCount: 320,
      status: true,
      companyName: 'Công ty TNHH XYZ',
      createdBy: 'Trần Thị Manager',
      createdAt: '2024-01-20T09:30:00Z',
      source: 'Facebook',
      region: 'Miền Bắc',
      address: '456 Đường Lê Lợi',
      city: 'Hà Nội',
      district: 'Quận Hoàn Kiếm',
      ward: 'Phường Hàng Bạc',
      email: '<EMAIL>',
      phone: '0987654321',
      position: 'Phó Giám đốc',
      firstAccessTime: '2024-03-19T09:30:00Z',
      accessCount30Days: 8,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [
                { key: '1-1-2', title: 'Thương mại', value: '1-1-2' },
                { key: '1-1-3', title: 'Dịch vụ', value: '1-1-3' }
              ]
            },
            {
              key: '1-3',
              title: 'Số lượng nhân sự công ty',
              value: '1-3',
              children: [
                { key: '1-3-2', title: '51-150', value: '1-3-2' },
                { key: '1-3-3', title: '151-300', value: '1-3-3' }
              ]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-2',
              title: 'Khu vực',
              value: '2-2',
              children: [{ key: '2-2-1', title: 'Bắc', value: '2-2-1' }]
            }
          ]
        }
      ]
    },
    {
      id: '3',
      code: 'PK003',
      name: 'Khách hàng Tiềm năng',
      customerCount: 85,
      status: false,
      companyName: 'Công ty TNHH DEF',
      createdBy: 'Lê Văn Sales',
      createdAt: '2024-02-10T10:15:00Z',
      source: 'Google',
      region: 'Miền Trung',
      address: '789 Đường Trần Phú',
      city: 'Đà Nẵng',
      district: 'Quận Hải Châu',
      ward: 'Phường Thạch Thang',
      email: '<EMAIL>',
      phone: '0369852147',
      position: 'Trưởng phòng Kinh doanh',
      firstAccessTime: '2024-03-18T10:15:00Z',
      accessCount30Days: 12,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-1', title: 'Sản xuất', value: '1-1-1' }]
            },
            {
              key: '1-3',
              title: 'Số lượng nhân sự công ty',
              value: '1-3',
              children: [
                { key: '1-3-1', title: 'Dưới 50', value: '1-3-1' },
                { key: '1-3-2', title: '51-150', value: '1-3-2' }
              ]
            }
          ]
        },
        {
          key: '5',
          title: '5. Thông tin cá nhân',
          value: '5',
          children: [
            {
              key: '5-1',
              title: 'Độ tuổi',
              value: '5-1',
              children: [
                { key: '5-1-2', title: '26 đến 40', value: '5-1-2' },
                { key: '5-1-3', title: '41 đến 50', value: '5-1-3' }
              ]
            }
          ]
        }
      ]
    },
    {
      id: '4',
      code: 'PK004',
      name: 'Khách hàng Doanh nghiệp',
      customerCount: 45,
      status: true,
      companyName: 'Công ty TNHH GHI',
      createdBy: 'Phạm Thị HR',
      createdAt: '2024-02-25T14:20:00Z',
      source: 'LinkedIn',
      region: 'Miền Nam',
      address: '321 Đường Nguyễn Văn Linh',
      city: 'Cần Thơ',
      district: 'Quận Ninh Kiều',
      ward: 'Phường An Khánh',
      email: '<EMAIL>',
      phone: '0852147963',
      position: 'Giám đốc Tài chính',
      firstAccessTime: '2024-03-17T14:20:00Z',
      accessCount30Days: 5,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-2',
              title: 'Thị trường của khách hàng',
              value: '1-2',
              children: [{ key: '1-2-2', title: 'Xuất khẩu', value: '1-2-2' }]
            },
            {
              key: '1-4',
              title: 'Chủ đầu tư nước ngoài',
              value: '1-4',
              children: [{ key: '1-4-1', title: 'Có', value: '1-4-1' }]
            }
          ]
        },
        {
          key: '3',
          title: '3. Tài chính khách hàng',
          value: '3',
          children: [
            { key: '3-2', title: 'Công nợ', value: '3-2' },
            { key: '3-6', title: 'Tăng trưởng Doanh số theo năm', value: '3-6' }
          ]
        }
      ]
    },
    {
      id: '5',
      code: 'PK005',
      name: 'Khách hàng Cá nhân',
      customerCount: 280,
      status: true,
      companyName: 'Công ty TNHH JKL',
      createdBy: 'Hoàng Văn Finance',
      createdAt: '2024-03-05T11:45:00Z',
      source: 'Website',
      region: 'Miền Bắc',
      address: '654 Đường Lạch Tray',
      city: 'Hải Phòng',
      district: 'Quận Ngô Quyền',
      ward: 'Phường Đông Khê',
      email: '<EMAIL>',
      phone: '0741852963',
      position: 'Kế toán trưởng',
      firstAccessTime: '2024-03-16T11:45:00Z',
      accessCount30Days: 20,
      evaluationCriteria: [
        {
          key: '4',
          title: '4. Thói quen tiêu dùng',
          value: '4',
          children: [
            { key: '4-1', title: 'Thị hiếu khách', value: '4-1' },
            { key: '4-4', title: 'Số đơn đặt hàng trong 1 năm', value: '4-4' }
          ]
        },
        {
          key: '5',
          title: '5. Thông tin cá nhân',
          value: '5',
          children: [
            {
              key: '5-2',
              title: 'Giới tính',
              value: '5-2',
              children: [
                { key: '5-2-1', title: 'Nam', value: '5-2-1' },
                { key: '5-2-2', title: 'Nữ', value: '5-2-2' }
              ]
            },
            {
              key: '5-3',
              title: 'Quốc tịch',
              value: '5-3',
              children: [{ key: '5-3-1', title: 'Việt Nam', value: '5-3-1' }]
            }
          ]
        }
      ]
    },
    {
      id: '6',
      code: 'PK006',
      name: 'Khách hàng Đối tác',
      customerCount: 65,
      status: false,
      companyName: 'Công ty TNHH MNO',
      createdBy: 'Vũ Thị Partner',
      createdAt: '2024-03-12T13:45:00Z',
      source: 'Zalo',
      region: 'Miền Nam',
      address: '789 Đường Võ Văn Tần',
      city: 'TP. Hồ Chí Minh',
      district: 'Quận 3',
      ward: 'Phường Võ Thị Sáu',
      email: '<EMAIL>',
      phone: '0963258741',
      position: 'Trưởng phòng Nhân sự',
      firstAccessTime: '2024-03-15T13:45:00Z',
      accessCount30Days: 7,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-3', title: 'Dịch vụ', value: '1-1-3' }]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-1',
              title: 'Châu lục',
              value: '2-1',
              children: [{ key: '2-1-1', title: 'Châu Á', value: '2-1-1' }]
            }
          ]
        }
      ]
    },
    {
      id: '7',
      code: 'PK007',
      name: 'Khách hàng Xuất khẩu',
      customerCount: 32,
      status: true,
      companyName: 'Công ty TNHH PQR',
      createdBy: 'Đỗ Văn Export',
      createdAt: '2024-03-18T15:30:00Z',
      source: 'Website',
      region: 'Miền Trung',
      address: '456 Đường Nguyễn Văn Linh',
      city: 'Nha Trang',
      district: 'Quận Lộc Thọ',
      ward: 'Phường Vĩnh Hòa',
      email: '<EMAIL>',
      phone: '0852369741',
      position: 'Phó Giám đốc Kinh doanh',
      firstAccessTime: '2024-03-14T15:30:00Z',
      accessCount30Days: 18,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-2',
              title: 'Thị trường của khách hàng',
              value: '1-2',
              children: [{ key: '1-2-2', title: 'Xuất khẩu', value: '1-2-2' }]
            }
          ]
        },
        {
          key: '3',
          title: '3. Tài chính khách hàng',
          value: '3',
          children: [
            { key: '3-1', title: 'Doanh số', value: '3-1' },
            { key: '3-3', title: 'Công nợ trung bình quá hạn', value: '3-3' },
            {
              key: '3-5',
              title: 'Doanh thu luỹ kế so với cùng kỳ năm trước',
              value: '3-5'
            }
          ]
        }
      ]
    },
    {
      id: '8',
      code: 'PK008',
      name: 'Khách hàng Nội địa',
      customerCount: 420,
      status: true,
      companyName: 'Công ty TNHH STU',
      createdBy: 'Ngô Thị Domestic',
      createdAt: '2024-03-20T09:15:00Z',
      source: 'Facebook',
      region: 'Miền Nam',
      address: '123 Đường Thống Nhất',
      city: 'Vũng Tàu',
      district: 'Quận 1',
      ward: 'Phường Thắng Tam',
      email: '<EMAIL>',
      phone: '0963258741',
      position: 'Trưởng phòng Kỹ thuật',
      firstAccessTime: '2024-03-13T09:15:00Z',
      accessCount30Days: 9,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-2',
              title: 'Thị trường của khách hàng',
              value: '1-2',
              children: [{ key: '1-2-1', title: 'Nội địa', value: '1-2-1' }]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-2',
              title: 'Khu vực',
              value: '2-2',
              children: [{ key: '2-2-3', title: 'Nam', value: '2-2-3' }]
            }
          ]
        }
      ]
    },
    {
      id: '9',
      code: 'PK009',
      name: 'Khách hàng Bán lẻ',
      customerCount: 180,
      status: false,
      companyName: 'Công ty TNHH VWX',
      createdBy: 'Trịnh Văn Retail',
      createdAt: '2024-03-22T11:20:00Z',
      source: 'Google',
      region: 'Miền Trung',
      address: '321 Đường Lê Lợi',
      city: 'Huế',
      district: 'Quận Thừa Thiên',
      ward: 'Phường Phú Hội',
      email: '<EMAIL>',
      phone: '0852369741',
      position: 'Giám đốc Tài chính',
      firstAccessTime: '2024-03-12T11:20:00Z',
      accessCount30Days: 14,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-2', title: 'Thương mại', value: '1-1-2' }]
            }
          ]
        },
        {
          key: '4',
          title: '4. Thói quen tiêu dùng',
          value: '4',
          children: [
            { key: '4-2', title: 'Tuổi khách hàng', value: '4-2' },
            {
              key: '4-3',
              title: 'Biết đến sản phẩm của công ty qua kênh nào?',
              value: '4-3'
            }
          ]
        }
      ]
    },
    {
      id: '10',
      code: 'PK010',
      name: 'Khách hàng Bán buôn',
      customerCount: 95,
      status: true,
      companyName: 'Công ty TNHH YZ',
      createdBy: 'Lý Thị Wholesale',
      createdAt: '2024-03-25T16:45:00Z',
      source: 'LinkedIn',
      region: 'Miền Trung',
      address: '654 Đường Nguyễn Huệ',
      city: 'Quy Nhơn',
      district: 'Quận Lê Hồng Phong',
      ward: 'Phường Trần Phú',
      email: '<EMAIL>',
      phone: '0963258741',
      position: 'Trưởng phòng Marketing',
      firstAccessTime: '2024-03-11T16:45:00Z',
      accessCount30Days: 11,
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-2', title: 'Thương mại', value: '1-1-2' }]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-2',
              title: 'Khu vực',
              value: '2-2',
              children: [{ key: '2-2-2', title: 'Trung', value: '2-2-2' }]
            }
          ]
        },
        {
          key: '5',
          title: '5. Thông tin cá nhân',
          value: '5',
          children: [
            {
              key: '5-1',
              title: 'Độ tuổi',
              value: '5-1',
              children: [
                { key: '5-1-1', title: '18 đến 25', value: '5-1-1' },
                { key: '5-1-2', title: '26 đến 40', value: '5-1-2' }
              ]
            }
          ]
        }
      ]
    }
  ],
  total: 10
}

export const useContactEvaluation = () => {
  return {
    data: DUMMY_DATA,
    total: DUMMY_DATA.total,
    isLoading: false,
    isError: false
  }
}

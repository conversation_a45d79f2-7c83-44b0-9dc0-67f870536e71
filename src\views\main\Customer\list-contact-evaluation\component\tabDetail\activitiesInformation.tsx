import React, { useState } from 'react'
import { Row, Col, Card, message, Tabs } from 'antd'
import { ICustomerVisit, IMission } from '~/dto/mission.dto'
import { ActivityChart } from '~/components'
import MissionTable from './activities-tab/Missions/components/MissionTable'
import CustomerVisitTable from './activities-tab/CustomerContact/components/CustomerVisitTable'
import {
  mockActivitySegments,
  mockMissions,
  mockCustomerVisits
} from '~/common/constants'

const { TabPane } = Tabs

export const ActivitiesInformation = () => {
  const [loading, setLoading] = useState(false)

  const style = {
    padding: '16px',
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    borderRadius: '12px',
    border: '1px solid rgba(0,0,0,0.05)',
    backgroundColor: 'white'
  }

  return (
    <div
      style={{
        minHeight: '100vh',
        marginTop: '16px'
      }}>
      <Row gutter={[24, 24]}>
        {/* Biểu đồ */}
        <Col xs={24} lg={24} style={style}>
          <Card title='Thống kê hoạt động theo segment' bordered={false}>
            <ActivityChart data={mockActivitySegments} />
          </Card>
        </Col>

        {/* Tabs cho 2 bảng */}
        <Col xs={24} lg={24} style={style}>
          <Card title='Quản lý hoạt động' bordered={false}>
            <Tabs defaultActiveKey='1' type='card'>
              <TabPane tab='Nhiệm vụ' key='1'>
                <MissionTable data={mockMissions} loading={loading} />
              </TabPane>
              <TabPane tab='Thăm hỏi khách hàng' key='2'>
                <CustomerVisitTable
                  data={mockCustomerVisits}
                  loading={loading}
                />
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

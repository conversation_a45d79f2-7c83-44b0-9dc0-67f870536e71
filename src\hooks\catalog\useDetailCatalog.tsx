import { useQuery } from '@tanstack/react-query'
import { rootApiService } from '~/services/@common'
import { IProductDetail } from '~/dto/product.dto'
import { endpoint_catalog } from '~/services/endpoints'

//interface catalog
export interface ICatalogDetail {
  id: string
  code: string
  name: string
  type: string
  unit: string
  unitPrice: number
  currency: string
  description: string
  images: string[]
  attachments: string[]
  updatedDate: string
  createdDate: string
  status: string
}
export const useDetailCatalog = (id: string, enabled: boolean = false) => {
  const { data, isLoading, refetch } = useQuery<ICatalogDetail>({
    enabled: enabled,
    queryKey: [endpoint_catalog.detail, id],
    queryFn: () => rootApiService.get(endpoint_catalog.detail, { id }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000 // 5 minutes
  })

  return {
    data,
    isLoading,
    refetch
  }
}

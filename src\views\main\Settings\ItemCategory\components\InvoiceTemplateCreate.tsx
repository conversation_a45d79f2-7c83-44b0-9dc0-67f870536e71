import { SaveOutlined } from '@ant-design/icons'
import { Card, Form, Input, Select } from 'antd'
import { Button } from 'antd/lib'
import BaseModal from '~/components/BaseModal'

export const InvoiceTemplateCreateComponent = ({ open, onClose, onSuccess }) => {
  const [form] = Form.useForm()

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      console.log('Submitted values:', values)
      // Gọi API hoặc xử lý logic tạo nhóm ở đây
      onSuccess?.()
      onClose()
    } catch (err) {
      console.log('Validation failed:', err)
    }
  }

  const contentModal = (
    <Card>
      <Form layout='vertical' form={form} onFinish={handleSubmit}>
        {/* 
            Mã hóa đơn
            Đơn vị phát hành enum
            Khách hàng áp dụng enum
            Loại hình dịch vụ enum
          */}
        <Form.Item label='Mã hóa đơn' name='code' rules={[{ required: true }]}>
          <Input placeholder='Nhập mã hóa đơn' />
        </Form.Item>
        <Form.Item label='Đơn vị phát hành' name='issuingUnit' rules={[{ required: true }]}>
          <Select>
            <Select.Option value='MISA'>MISA</Select.Option>
            <Select.Option value='VNPT Invoice'>VNPT Invoice</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label='Khách hàng áp dụng' name='customerName' rules={[{ required: true }]}>
          <Select>
            <Select.Option value='Công ty ABC'>Công ty ABC</Select.Option>
            <Select.Option value='Công ty XYZ'>Công ty XYZ</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label='Loại hình dịch vụ' name='serviceType' rules={[{ required: true }]}>
          <Select>
            <Select.Option value='Logistic'>Vận chuyển</Select.Option>
            <Select.Option value='Other'>Khác</Select.Option>
          </Select>
        </Form.Item>
        {/* Trạng thái */}

        <Form.Item style={{ textAlign: 'right' }}>
          {/* Clear */}
          <Button onClick={() => form.resetFields()} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Lưu
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )
  return (
    <BaseModal
      width={800}
      open={open}
      title='Tạo mẫu hóa đơn mới'
      description='Thêm mẫu hóa đơn mới vào hệ thống'
      onClose={onClose}
      childrenBody={contentModal}
    />
  )
}

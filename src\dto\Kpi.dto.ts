export interface IKpiCategory {
  kpiCode: string
  kpiName: string
  department: string
  unit: string
  frequency: string
  status: 'Hoạt động' | 'Ngưng hoạt động'
}

export interface IKpiCategoryResponse {
  data: IKpiCategory[]
  total: number
}

//filterr
export interface IKpiCategoryFilter {
  kpiCode?: string
  kpiName?: string
  department?: string
  unit?: string
  frequency?: string
  status?: 'Hoạt động' | 'Ngưng hoạt động'
  pageIndex?: number
  pageSize?: number
}

export interface IKpiGroup {
  code: string // KPI group code
  groupName: string // Tên Bộ KPI
  department: string // Phòng ban
  //tần suất đo
  frequency: string
  createdBy: string // Người tạo
  createdDate: string // Ngày tạo (dd/MM/yyyy)
  updatedDate: string // Ngày cập nhật (dd/MM/yyyy)
  status: 'Hoạt động' | 'Ngưng hoạt động'
}

export interface IKpiGroupResponse {
  data: IKpiGroup[]
  total: number
}

export interface IKpiGroupFilter {
  groupName?: string
  department?: string
  createdBy?: string
  createdDate?: string
  updatedDate?: string
  status?: 'Hoạt động' | 'Ngưng hoạt động'
  pageIndex?: number
  pageSize?: number
}

export interface IKpiEvaluate {
  code: string
  evaluateMonth: string
  kpiGroup: string
  department: string
  departmentData: any[]
  unit: string
  createdBy: string
  createdDate: string
  approvedBy: string
  approvedDate: string
  status: 'Chờ duyệt' | 'Đã duyệt'
}

export interface IKpiEvaluateResponse {
  data: IKpiEvaluate[]
  total: number
}

export interface IKpiEvaluateFilter {
  createdDate?: string
  status?: 'Chờ duyệt' | 'Đã duyệt'
  pageIndex?: number
  pageSize?: number
}

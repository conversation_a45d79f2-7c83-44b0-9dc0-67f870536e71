import React, { useState, useEffect } from 'react'
import screenfull from 'screenfull'
import { message, Tag, Tooltip } from 'antd'
import './index.less'
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'

const FullScreen = () => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const change = () => {
    setIsFullscreen(!screenfull.isEnabled)
  }
  const click = () => {
    if (!screenfull.isEnabled) {
      message.warning('you browser can not work')
      return false
    }
    screenfull.toggle()
    return true
  }
  useEffect(() => {
    screenfull.isEnabled && screenfull.on('change', change)
    return () => {
      screenfull.isEnabled && screenfull.off('change', change)
    }
  }, [])
  const title = isFullscreen ? 'Cancel full screen' : 'Full screen'
  return (
    <div className='fullScreen-container'>
      <Tooltip placement='bottom' title={title}>
        <Tag
          style={{
            cursor: 'pointer',
            fontSize: '25px',
            width: '40px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onClick={click}>
          {isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
        </Tag>
      </Tooltip>
    </div>
  )
}

export default FullScreen

import { useQuery } from "@tanstack/react-query";
import { PageResponse } from "~/@ui/GridControl/models";
import { IDistrict } from "~/dto/provinces.dto";
import { rootApiService } from "~/services/@common";
import { endpoints_provinces } from "~/services/endpoints";

interface UseListDistrictsParams {
  code?: number;
}

export const useListDistricts = ({ code }: UseListDistrictsParams) => {
  const { data, isLoading, refetch } = useQuery<PageResponse<IDistrict>>({
    queryKey: [endpoints_provinces.listDistrict, { code }],
    queryFn: () =>
      rootApiService.post<PageResponse<IDistrict>>(
        endpoints_provinces.listDistrict,
        { code }
      ),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!code,
  });

  const districts: any = data || [];

  return {
    data: districts,
    isLoading,
    refetch,
  };
};

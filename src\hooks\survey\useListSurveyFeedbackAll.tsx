import { useQuery } from '@tanstack/react-query'
import { rootApiService } from '~/services/@common'
import { endpoints_customer } from '~/services/endpoints'

export const useListSurveyFeedbackAll = () => {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [endpoints_customer.listAll],
    queryFn: () => rootApiService.get(endpoints_customer.listAll)
  })

  return {
    data,
    isLoading,
    refetch
  }
}

import { useQuery } from '@tanstack/react-query'
import { IGiftResponse } from '~/dto/gift.dto'

const DUMMY_GIFT: IGiftResponse = {
  data: [
    {
      id: '1',
      targetAudience: 'Kh<PERSON>ch hàng thân thiết',
      programName: 'Tích điểm đổi quà',
      memberLevel: 'Bạc',
      conversionType: 'Điểm',
      giftValue: 100,
      unit: 'VNĐ',
      startDate: '2021-01-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Chương trình tích điểm đổi quà cho khách hàng thân thiết',
      createdAt: '2021-01-01',
      updatedAt: '2021-01-01'
    },
    {
      id: '2',
      targetAudience: 'Khách hàng VIP',
      programName: 'Ưu đãi sinh nhật',
      memberLevel: 'Vàng',
      conversionType: 'Quà tặng',
      giftValue: 200,
      unit: 'VNĐ',
      startDate: '2021-02-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Tặng quà sinh nhật cho khách hàng VIP',
      createdAt: '2021-02-01',
      updatedAt: '2021-02-01'
    },
    {
      id: '3',
      targetAudience: 'Khách hàng mới',
      programName: 'Chào mừng thành viên mới',
      memberLevel: 'Đồng',
      conversionType: 'Voucher',
      giftValue: 50,
      unit: 'VNĐ',
      startDate: '2021-03-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Tặng voucher cho khách hàng đăng ký mới',
      createdAt: '2021-03-01',
      updatedAt: '2021-03-01'
    },
    {
      id: '4',
      targetAudience: 'Khách hàng thân thiết',
      programName: 'Tích lũy điểm',
      memberLevel: 'Bạc',
      conversionType: 'Điểm',
      giftValue: 120,
      unit: 'VNĐ',
      startDate: '2021-04-01',
      endDate: '2021-12-31',
      status: 'INACTIVE',
      description: 'Tích lũy điểm cho mỗi giao dịch',
      createdAt: '2021-04-01',
      updatedAt: '2021-04-01'
    },
    {
      id: '5',
      targetAudience: 'Khách hàng VIP',
      programName: 'Hoàn tiền cuối năm',
      memberLevel: 'Vàng',
      conversionType: 'Hoàn tiền',
      giftValue: 300,
      unit: 'VNĐ',
      startDate: '2021-05-01',
      endDate: '2021-12-31',
      status: 'DRAFT',
      description: 'Hoàn tiền cho khách hàng VIP cuối năm',
      createdAt: '2021-05-01',
      updatedAt: '2021-05-01'
    },
    {
      id: '6',
      targetAudience: 'Khách hàng doanh nghiệp',
      programName: 'Ưu đãi doanh nghiệp',
      memberLevel: 'Kim cương',
      conversionType: 'Quà tặng',
      giftValue: 500,
      unit: 'VNĐ',
      startDate: '2021-06-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Ưu đãi đặc biệt cho khách hàng doanh nghiệp',
      createdAt: '2021-06-01',
      updatedAt: '2021-06-01'
    },
    {
      id: '7',
      targetAudience: 'Khách hàng thân thiết',
      programName: 'Tặng điểm ngày lễ',
      memberLevel: 'Bạc',
      conversionType: 'Điểm',
      giftValue: 80,
      unit: 'VNĐ',
      startDate: '2021-07-01',
      endDate: '2021-12-31',
      status: 'EXPIRED',
      description: 'Tặng điểm cho khách hàng vào các ngày lễ',
      createdAt: '2021-07-01',
      updatedAt: '2021-07-01'
    },
    {
      id: '8',
      targetAudience: 'Khách hàng mới',
      programName: 'Giảm giá lần đầu',
      memberLevel: 'Đồng',
      conversionType: 'Giảm giá',
      giftValue: 70,
      unit: 'VNĐ',
      startDate: '2021-08-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Giảm giá cho khách hàng mua lần đầu',
      createdAt: '2021-08-01',
      updatedAt: '2021-08-01'
    },
    {
      id: '9',
      targetAudience: 'Khách hàng VIP',
      programName: 'Tặng quà cuối năm',
      memberLevel: 'Vàng',
      conversionType: 'Quà tặng',
      giftValue: 400,
      unit: 'VNĐ',
      startDate: '2021-09-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Tặng quà cho khách hàng VIP dịp cuối năm',
      createdAt: '2021-09-01',
      updatedAt: '2021-09-01'
    },
    {
      id: '10',
      targetAudience: 'Khách hàng doanh nghiệp',
      programName: 'Tích điểm doanh nghiệp',
      memberLevel: 'Kim cương',
      conversionType: 'Điểm',
      giftValue: 600,
      unit: 'VNĐ',
      startDate: '2021-10-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Tích điểm cho khách hàng doanh nghiệp',
      createdAt: '2021-10-01',
      updatedAt: '2021-10-01'
    },
    {
      id: '11',
      targetAudience: 'Khách hàng thân thiết',
      programName: 'Tặng voucher sinh nhật',
      memberLevel: 'Bạc',
      conversionType: 'Voucher',
      giftValue: 90,
      unit: 'VNĐ',
      startDate: '2021-11-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Tặng voucher cho khách hàng vào dịp sinh nhật',
      createdAt: '2021-11-01',
      updatedAt: '2021-11-01'
    },
    {
      id: '12',
      targetAudience: 'Khách hàng mới',
      programName: 'Tặng điểm đăng ký',
      memberLevel: 'Đồng',
      conversionType: 'Điểm',
      giftValue: 60,
      unit: 'VNĐ',
      startDate: '2021-12-01',
      endDate: '2021-12-31',
      status: 'ACTIVE',
      description: 'Tặng điểm cho khách hàng đăng ký mới',
      createdAt: '2021-12-01',
      updatedAt: '2021-12-01'
    }
  ],
  total: 12
}

export const useGift = () => {
  // const { data, isLoading } = useQuery({
  //   queryKey: ['loyalty'],
  //   queryFn: () => DUMMY_GIFT
  // })

  return {
    data: DUMMY_GIFT,
    isLoading: false,
    refetch: () => {},
    total: DUMMY_GIFT.total
  }
}

import React, { useState } from 'react'
import { Row, Col } from 'antd'
import CustomerVisitTable from './components/CustomerVisitTable'
import { mockCustomerVisits } from '~/common/constants'

export const CustomerContactView = () => {
  const [loading, setLoading] = useState(false)

  return (
    <div
      style={{
        padding: '24px',
        backgroundColor: '#f0f2f5',
        minHeight: '100vh'
      }}>
      <Row gutter={[24, 24]}>
        {/* Bảng Thăm hỏi khách hàng */}
        <Col xs={24} lg={24}>
          <CustomerVisitTable data={mockCustomerVisits} loading={loading} />
        </Col>
      </Row>
    </div>
  )
}

import { Row, Col } from 'antd'
import { useState } from 'react'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import FilterProduct from './components/FilterProduct'
import { ISettingContract, ISettingContractFilter } from '~/dto/setting-contract.dto'
import { DeleteOutlined } from '@ant-design/icons'
import moment from 'moment'
import { BaseButton } from '~/components'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'

export const ContractView = () => {
  const [filter, setFilter] = useState<ISettingContractFilter>({
    id: null,
    name: '',
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (values: ISettingContractFilter) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      id: null,
      name: '',
      pageIndex: 0,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: 0,
      pageSize: 10
    })
  }

  //fakeData
  const [fakeData, setFakeData] = useState<ISettingContract[]>([
    {
      id: '1',
      code: 'HD001',
      name: 'Hợp đồng dịch vụ A',
      status: 'Hoạt động',
      terms: [
        { code: '1', name: 'Điều khoản 1', content: 'Nội dung điều khoản 1' },
        { code: '2', name: 'Điều khoản 2', content: 'Nội dung điều khoản 2' }
      ],
      baseOn: 'Căn cứ ký kết A',
      description: 'Mô tả A',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '2',
      code: 'HD002',
      name: 'Hợp đồng dịch vụ B',
      status: 'Hoạt động',
      terms: [
        { code: '1', name: 'Điều khoản 1', content: 'Nội dung điều khoản 1' },
        { code: '2', name: 'Điều khoản 2', content: 'Nội dung điều khoản 2' }
      ],
      baseOn: 'Căn cứ ký kết B',
      description: 'Mô tả B',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '3',
      code: 'HD003',
      name: 'Hợp đồng dịch vụ C',
      status: 'Hoạt động',
      terms: [
        { code: '1', name: 'Điều khoản 1', content: 'Nội dung điều khoản 1' },
        { code: '2', name: 'Điều khoản 2', content: 'Nội dung điều khoản 2' }
      ],
      baseOn: 'Căn cứ ký kết C',
      description: 'Mô tả C',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '4',
      code: 'HD004',
      name: 'Hợp đồng dịch vụ D',
      status: 'Hoạt động',
      terms: [
        { code: '1', name: 'Điều khoản 1', content: 'Nội dung điều khoản 1' },
        { code: '2', name: 'Điều khoản 2', content: 'Nội dung điều khoản 2' }
      ],
      baseOn: 'Căn cứ ký kết D',
      description: 'Mô tả D',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    },
    {
      id: '5',
      code: 'HD005',
      name: 'Hợp đồng dịch vụ E',
      status: 'Hoạt động',
      terms: [
        { code: '1', name: 'Điều khoản 1', content: 'Nội dung điều khoản 1' },
        { code: '2', name: 'Điều khoản 2', content: 'Nội dung điều khoản 2' }
      ],
      baseOn: 'Căn cứ ký kết E',
      description: 'Mô tả E',
      createdAt: '2024-03-20T08:30:00',
      updatedAt: '2024-03-20T08:30:00'
    }
  ])

  //col
  const columns: any[] = [
    {
      title: 'Mã hợp đồng',
      dataIndex: 'id',
      align: 'center',
      key: 'id',
      width: 100
    },
    {
      title: 'Tên hợp đồng',
      dataIndex: 'name',
      align: 'center',
      key: 'name',
      width: 200
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      align: 'center',
      key: 'description',
      width: 200
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      align: 'center',
      key: 'status',
      width: 100
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      align: 'center',
      key: 'createdAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      align: 'center',
      key: 'updatedAt',
      width: 150,
      render: (value: string) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]

  //handle delete
  const handleDelete = (record: any) => {
    setFakeData(fakeData.filter((item) => item.id !== record.id))
  }

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={fakeData}
            total={0}
            isLoading={false}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

import { But<PERSON>, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { EProduct, NSProduct } from '~/common/enums/NSProduct'
import { IFilterReportKpiSaleEmployee } from '~/dto/report-kpi-sale-employee.dto'

interface IProps {
  onFilter: (values: IFilterReportKpiSaleEmployee) => void
  onReset: () => void
  isLoading: boolean
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>          <Row gutter={24}>
            <Col span={6}>
              <Form.Item initialValue={null} label='Mã Nhân viên' name='code'>
                <Input placeholder='Mã nhân viên' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item initialValue={null} label='Tên nhân viên' name='salesRep'>
                <Input placeholder='Nhập tên nhân viên' />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item initialValue={null} label='KPI từ - đến (%)' name='month'>
                <Input type='number' min={0} placeholder='Nhập KPI từ' />
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item initialValue={null} label=' ' name='month'>
                <Input type='number' placeholder='Nhập KPI đến' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item initialValue={null} label='Tháng' name='month'>
                <DatePicker format={'MM/YYYY'} picker='month' style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Row>
          <div
            style={{
              gap: 10,
              //align item center
              display: 'flex',
              justifyContent: 'center',
              marginTop: 10,
              width: '100%'
            }}>
            <Button
              type='primary'

              htmlType='submit'
              onClick={handleFilter}
              loading={isLoading}>
              <SearchOutlined />
              Tìm kiếm
            </Button>
            <Button type='default' htmlType='submit' onClick={handleReset}>
              <ReloadOutlined />
              Làm mới
            </Button>
          </div>
        </Row>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterProduct

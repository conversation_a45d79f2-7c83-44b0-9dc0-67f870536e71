@light-1: #fff !important;
@dark-1: #000 !important;
@dark-2: #1f1f1f !important;

// <PERSON><PERSON> dụng CSS Variables để tương thích với TypeScript constants
:root {
  --color-white: #fff;
  --color-black: #000;
  --color-dark-2: #1f1f1f;
  --color-gray-shadow: #dddddd;
}

[data-theme='light'] {
  background-color: var(--color-white);

  .bg-1,
  .bg-2 {
    background-color: var(--color-white);
  }
  .ant-layout-sider {
    background-color: var(--color-white);
  }

  .text-1 {
    color: var(--color-black);
  }

  .text-2 {
    color: rgba(0, 0, 0, 0.85);
  }
}

[data-theme='dark'] {
  .bg-1 {
    background-color: var(--color-black);
  }
  .bg-2 {
    background-color: var(--color-dark-2);
  }

  .text-1 {
    color: var(--color-white);
  }

  .text-2 {
    color: rgba(255, 255, 255, 0.65);
  }
}

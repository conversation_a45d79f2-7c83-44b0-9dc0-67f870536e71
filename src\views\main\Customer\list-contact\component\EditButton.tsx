import { EditOutlined, SaveOutlined } from '@ant-design/icons'
import {
  Card,
  Tag,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  DatePicker,
  message,
  Switch
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import BaseModal from '~/components/BaseModal'
import { useEffect } from 'react'
import { useForm } from 'antd/es/form/Form'
import { ICustomerContact } from '~/dto/customer-contact.dto'
import dayjs from 'dayjs'
import { useModal } from '~/hooks/useModal'
import { useTranslation } from 'react-i18next'

const { Option } = Select

interface EditButtonProps {
  data: ICustomerContact
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { t } = useTranslation()
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()

  const {
    contactCode,
    name,
    customerName,
    phone,
    email,
    branch,
    note,
    isSpecialCare,
    status
  } = data || {}

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        contactCode,
        name,
        customerName,
        phone,
        email,
        branch,
        note,
        isSpecialCare,
        status
      })
    }
  }, [open, data, form])

  if (!data) return null

  const handleSave = async (values: any) => {
    if (!data) return
    const body = {
      ...values,
      id: data?.id
    }

    try {
      // TODO: Implement update customer contact mutation
      // await updateCustomerContact(body)
      closeModal()
      onSuccess && onSuccess()
      form.resetFields()
    } catch (error) {
      message.error('Cập nhật liên hệ thất bại')
    }
  }

  // Header thông tin liên hệ
  const contactHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>
              {t('customer:customer_contact.columns.contactCode')}:
            </BaseText>
            <br />
            <BaseText weight='bold'>{contactCode || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>
              {t('customer:customer_contact.columns.name')}:
            </BaseText>
            <br />
            <BaseText weight='bold'>{name || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>
              {t('customer:customer_contact.columns.status')}:
            </BaseText>
            <br />
            <Tag color={status === 'Hoạt động' ? 'green' : 'red'}>{status}</Tag>
          </div>
        </Col>
      </Row>
    </Card>
  )

  const modalContent = (
    <div>
      {contactHeader}

      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.contactCode')}
              name='contactCode'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer_contact.placeholders.enterContactCode'
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.name')}
              name='name'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer_contact.placeholders.enterName'
                )}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.customerName')}
              name='customerName'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer_contact.placeholders.enterCustomerName'
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.phone')}
              name='phone'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                },
                {
                  pattern: /^[0-9]{10}$/,
                  message: t(
                    'customer:customer_contact.validation.phoneInvalid'
                  )
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer_contact.placeholders.enterPhone'
                )}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.email')}
              name='email'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                },
                {
                  type: 'email',
                  message: t(
                    'customer:customer_contact.validation.emailInvalid'
                  )
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer_contact.placeholders.enterEmail'
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.branch')}
              name='branch'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                }
              ]}>
              <Input
                placeholder={t(
                  'customer:customer_contact.placeholders.enterBranch'
                )}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.isSpecialCare')}
              name='isSpecialCare'
              valuePropName='checked'>
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t('customer:customer_contact.columns.status')}
              name='status'
              rules={[
                {
                  required: true,
                  message: t('customer:customer_contact.validation.required')
                }
              ]}>
              <Select
                placeholder={t(
                  'customer:customer_contact.placeholders.enterStatus'
                )}>
                <Option value='Hoạt động'>Hoạt động</Option>
                <Option value='Không hoạt động'>Không hoạt động</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label={t('customer:customer_contact.columns.note')}
              name='note'>
              <Input.TextArea
                placeholder={t(
                  'customer:customer_contact.placeholders.enterNote'
                )}
                rows={4}
              />
            </Form.Item>
          </Col>
        </Row>

        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            {t('customer:customer_contact.cancel')}
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            {t('customer:customer_contact.edit')}
          </Button>
        </div>
      </Form>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        tooltip={t('customer:customer_contact.edit')}
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title={t('customer:customer_contact.edit')}
        description={t('customer:customer_contact.description')}
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton

import { FC } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'

interface ICustomerInfo {
  id: string
  crmCode: string
  sapCode: string
  customerName: string
  address: string
  phone: string
  email: string
  salesRep: string
  salesDepartment: string
  employeeCount: string
  area: string
  province: string
  district: string
  ward: string
  revenue: number
  growthCurrentPeriod: number
  growthPreviousPeriod: number
  serviceYears: number
  monthsSinceLastPurchase: number
}

type IProps = {}

const dummyData: ICustomerInfo[] = [
  {
    id: '1',
    crmCode: 'CRM001',
    sapCode: 'SAP001',
    customerName: 'Công ty TNHH ABC',
    address: '123 Đường Lê Lợi, Phường Bến Nghé, Quận 1, TP.HCM',
    phone: '028-1234-5678',
    email: '<EMAIL>',
    salesRep: '<PERSON><PERSON><PERSON><PERSON>',
    salesDepartment: '<PERSON>òng <PERSON>nh do<PERSON>h <PERSON>',
    employeeCount: '50-100',
    area: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 1',
    ward: 'Phường Bến Nghé',
    revenue: 1500000000,
    growthCurrentPeriod: 15.5,
    growthPreviousPeriod: 12.3,
    serviceYears: 5,
    monthsSinceLastPurchase: 2
  },
  {
    id: '2',
    crmCode: 'CRM002',
    sapCode: 'SAP002',
    customerName: 'Công ty CP XYZ',
    address:
      '456 Đường Nguyễn Duy Trinh, Phường Bình Trưng Đông, Quận 2, TP.HCM',
    phone: '028-9876-5432',
    email: '<EMAIL>',
    salesRep: 'Trần Thị B',
    salesDepartment: 'Phòng Kinh doanh Miền Nam',
    employeeCount: '100-200',
    area: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 2',
    ward: 'Phường Bình Trưng Đông',
    revenue: 2500000000,
    growthCurrentPeriod: 8.7,
    growthPreviousPeriod: 10.2,
    serviceYears: 3,
    monthsSinceLastPurchase: 1
  },
  {
    id: '3',
    crmCode: 'CRM003',
    sapCode: 'SAP003',
    customerName: 'Công ty TNHH DEF',
    address:
      '789 Đường Nguyễn Văn Linh, Phường Thạch Bàn, Quận Long Biên, Hà Nội',
    phone: '024-5555-6666',
    email: '<EMAIL>',
    salesRep: 'Lê Văn C',
    salesDepartment: 'Phòng Kinh doanh Miền Bắc',
    employeeCount: '200-500',
    area: 'Miền Bắc',
    province: 'Hà Nội',
    district: 'Quận Long Biên',
    ward: 'Phường Thạch Bàn',
    revenue: 3200000000,
    growthCurrentPeriod: 22.1,
    growthPreviousPeriod: 18.5,
    serviceYears: 7,
    monthsSinceLastPurchase: 0
  },
  {
    id: '4',
    crmCode: 'CRM004',
    sapCode: 'SAP004',
    customerName: 'Công ty CP GHI',
    address:
      '321 Đường Nguyễn Văn Linh, Phường Nam Dương, Quận Hải Châu, Đà Nẵng',
    phone: '0236-7777-8888',
    email: '<EMAIL>',
    salesRep: 'Phạm Thị D',
    salesDepartment: 'Phòng Kinh doanh Miền Trung',
    employeeCount: '50-100',
    area: 'Miền Trung',
    province: 'Đà Nẵng',
    district: 'Quận Hải Châu',
    ward: 'Phường Nam Dương',
    revenue: 980000000,
    growthCurrentPeriod: 5.3,
    growthPreviousPeriod: 7.8,
    serviceYears: 4,
    monthsSinceLastPurchase: 3
  },
  {
    id: '5',
    crmCode: 'CRM005',
    sapCode: 'SAP005',
    customerName: 'Công ty TNHH JKL',
    address: '654 Đường Nguyễn Thị Thập, Phường Tân Phú, Quận 7, TP.HCM',
    phone: '028-1111-2222',
    email: '<EMAIL>',
    salesRep: 'Hoàng Văn E',
    salesDepartment: 'Phòng Kinh doanh Miền Nam',
    employeeCount: '20-50',
    area: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 7',
    ward: 'Phường Tân Phú',
    revenue: 650000000,
    growthCurrentPeriod: 12.8,
    growthPreviousPeriod: 9.4,
    serviceYears: 2,
    monthsSinceLastPurchase: 1
  },
  {
    id: '6',
    crmCode: 'CRM006',
    sapCode: 'SAP006',
    customerName: 'Công ty CP MNO',
    address: '987 Đường Trần Phú, Phường Hải Châu 1, Quận Hải Châu, Đà Nẵng',
    phone: '0236-3333-4444',
    email: '<EMAIL>',
    salesRep: 'Vũ Thị F',
    salesDepartment: 'Phòng Kinh doanh Miền Trung',
    employeeCount: '100-200',
    area: 'Miền Trung',
    province: 'Đà Nẵng',
    district: 'Quận Hải Châu',
    ward: 'Phường Hải Châu 1',
    revenue: 1800000000,
    growthCurrentPeriod: -2.1,
    growthPreviousPeriod: 3.5,
    serviceYears: 6,
    monthsSinceLastPurchase: 4
  },
  {
    id: '7',
    crmCode: 'CRM007',
    sapCode: 'SAP007',
    customerName: 'Công ty TNHH PQR',
    address: '147 Đường Lý Thường Kiệt, Phường 10, Quận Tân Bình, TP.HCM',
    phone: '028-5555-7777',
    email: '<EMAIL>',
    salesRep: 'Đặng Văn G',
    salesDepartment: 'Phòng Kinh doanh Miền Nam',
    employeeCount: '500-1000',
    area: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận Tân Bình',
    ward: 'Phường 10',
    revenue: 4500000000,
    growthCurrentPeriod: 18.9,
    growthPreviousPeriod: 15.7,
    serviceYears: 8,
    monthsSinceLastPurchase: 0
  },
  {
    id: '8',
    crmCode: 'CRM008',
    sapCode: 'SAP008',
    customerName: 'Công ty CP STU',
    address:
      '258 Đường Nguyễn Trãi, Phường Thanh Xuân Trung, Quận Thanh Xuân, Hà Nội',
    phone: '024-8888-9999',
    email: '<EMAIL>',
    salesRep: 'Bùi Thị H',
    salesDepartment: 'Phòng Kinh doanh Miền Bắc',
    employeeCount: '200-500',
    area: 'Miền Bắc',
    province: 'Hà Nội',
    district: 'Quận Thanh Xuân',
    ward: 'Phường Thanh Xuân Trung',
    revenue: 2800000000,
    growthCurrentPeriod: 11.2,
    growthPreviousPeriod: 8.9,
    serviceYears: 4,
    monthsSinceLastPurchase: 2
  },
  {
    id: '9',
    crmCode: 'CRM009',
    sapCode: 'SAP009',
    customerName: 'Công ty TNHH VWX',
    address: '369 Đường Võ Văn Tần, Phường 6, Quận 3, TP.HCM',
    phone: '028-7777-8888',
    email: '<EMAIL>',
    salesRep: 'Lý Văn I',
    salesDepartment: 'Phòng Kinh doanh Miền Nam',
    employeeCount: '50-100',
    area: 'Miền Nam',
    province: 'TP.HCM',
    district: 'Quận 3',
    ward: 'Phường 6',
    revenue: 1200000000,
    growthCurrentPeriod: 6.8,
    growthPreviousPeriod: 4.2,
    serviceYears: 3,
    monthsSinceLastPurchase: 1
  },
  {
    id: '10',
    crmCode: 'CRM010',
    sapCode: 'SAP010',
    customerName: 'Công ty CP YZA',
    address: '741 Đường Lê Duẩn, Phường Thạch Thang, Quận Hải Châu, Đà Nẵng',
    phone: '0236-1111-2222',
    email: '<EMAIL>',
    salesRep: 'Ngô Thị K',
    salesDepartment: 'Phòng Kinh doanh Miền Trung',
    employeeCount: '20-50',
    area: 'Miền Trung',
    province: 'Đà Nẵng',
    district: 'Quận Hải Châu',
    ward: 'Phường Thạch Thang',
    revenue: 850000000,
    growthCurrentPeriod: -1.5,
    growthPreviousPeriod: 2.8,
    serviceYears: 5,
    monthsSinceLastPurchase: 5
  }
]

export const CustomerInformation: FC<IProps> = (props: IProps) => {
  const columns: ColumnsType<ICustomerInfo> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Mã CRM',
      dataIndex: 'crmCode',
      key: 'crmCode',
      width: 100
    },
    {
      title: 'Mã SAP',
      dataIndex: 'sapCode',
      key: 'sapCode',
      width: 100
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 300
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 130
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 180
    },
    {
      title: 'Nhân viên kinh doanh',
      dataIndex: 'salesRep',
      key: 'salesRep',
      width: 150
    },
    {
      title: 'Phòng ban NVKD',
      dataIndex: 'salesDepartment',
      key: 'salesDepartment',
      width: 180
    },
    {
      title: 'Số lượng nhân sự',
      dataIndex: 'employeeCount',
      key: 'employeeCount',
      width: 150
    },
    {
      title: 'Khu vực',
      dataIndex: 'area',
      key: 'area',
      width: 120
    },
    {
      title: 'Tỉnh/thành phố',
      dataIndex: 'province',
      key: 'province',
      width: 130
    },
    {
      title: 'Quận/Huyện',
      dataIndex: 'district',
      key: 'district',
      width: 130
    },
    {
      title: 'Phường/xã',
      dataIndex: 'ward',
      key: 'ward',
      width: 130
    },
    {
      title: 'Doanh số',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 150,
      render: (value: number) =>
        new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND',
          minimumFractionDigits: 0
        }).format(value)
    },
    {
      title: 'Tăng trưởng doanh số (cùng kỳ)',
      dataIndex: 'growthCurrentPeriod',
      key: 'growthCurrentPeriod',
      width: 180,
      render: (value: number) => (
        <Tag
          color={value >= 0 ? 'success' : 'error'}
          style={{ fontSize: '14px', padding: '4px 8px' }}>
          {value >= 0 ? '+' : ''}
          {value.toFixed(1)}%
        </Tag>
      )
    },
    {
      title: 'Tăng trưởng doanh số (Kỳ trước)',
      dataIndex: 'growthPreviousPeriod',
      key: 'growthPreviousPeriod',
      width: 180,
      render: (value: number) => (
        <Tag
          color={value >= 0 ? 'success' : 'error'}
          style={{ fontSize: '14px', padding: '4px 8px' }}>
          {value >= 0 ? '+' : ''}
          {value.toFixed(1)}%
        </Tag>
      )
    },
    {
      title: 'Số năm dùng dịch vụ',
      dataIndex: 'serviceYears',
      key: 'serviceYears',
      width: 150,
      render: (value: number) => `${value} năm`
    },
    {
      title: 'Số tháng gần nhất chưa mua hàng',
      dataIndex: 'monthsSinceLastPurchase',
      key: 'monthsSinceLastPurchase',
      width: 200,
      render: (value: number) => `${value} tháng`
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={dummyData}
            total={dummyData.length}
            isLoading={false}
            scroll={{ x: 3000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

import { But<PERSON>, Col, Collapse, Form, Input, Row, Select } from 'antd'
import { FC } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { IFilterLoyalty } from '~/dto/loyalty.dto'

interface IProps {
  onFilter: (values: IFilterLoyalty) => void
  onReset: () => void
  isLoading: boolean
}

const FilterLoyalty: FC<IProps> = ({ onFilter, onReset, isLoading }) => {
  const [form] = Form.useForm()

  const handleSubmit = (values: IFilterLoyalty) => {
    onFilter(values)
  }

  const handleReset = () => {
    form.resetFields()
    onReset()
  }

  return (
    <Collapse defaultActiveKey={['1']}>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form
          form={form}
          onFinish={handleSubmit}
          layout='vertical'
          className='filter-form'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name='customerCode' label='Mã khách hàng'>
                <Input placeholder='Nhập mã khách hàng' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name='customerName' label='Tên khách hàng'>
                <Input placeholder='Nhập tên khách hàng' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name='phoneNumber' label='Số điện thoại'>
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name='programCode' label='Mã chương trình'>
                <Input placeholder='Nhập mã chương trình' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name='programName' label='Tên chương trình'>
                <Input placeholder='Nhập tên chương trình' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name='memberRank' label='Hạng thành viên'>
                <Select placeholder='Chọn hạng thành viên' allowClear>
                  <Select.Option value='BRONZE'>Đồng</Select.Option>
                  <Select.Option value='SILVER'>Bạc</Select.Option>
                  <Select.Option value='GOLD'>Vàng</Select.Option>
                  <Select.Option value='PLATINUM'>Bạch kim</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} style={{ textAlign: 'right' }}>
              <Button
                type='primary'
                htmlType='submit'
                icon={<SearchOutlined />}
                loading={isLoading}
                style={{ marginRight: 8 }}>
                Tìm kiếm
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
                loading={isLoading}>
                Làm mới
              </Button>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterLoyalty

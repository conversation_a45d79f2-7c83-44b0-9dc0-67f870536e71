import React from 'react'
import {
  Card,
  Form,
  Input,
  DatePicker,
  Select,
  Button,
  Table,
  Typography,
  Space,
  InputNumber,
} from 'antd'
import dayjs from 'dayjs'
import { PlusOutlined, SaveOutlined } from '@ant-design/icons'

const { Title } = Typography
const { RangePicker } = DatePicker

interface QuotationService {
  key: string
  name: string
  unit: string
  price: number
  vat: string
}

interface QuotationTemplate {
  templateName: string
  templateCode: string
  applicableUnits: string[]
  effectiveDate: string
  expiryDate?: string
  serviceType: string
  services: QuotationService[]
}

const mockDetail: QuotationTemplate = {
  templateName: 'Template HCM → HN',
  templateCode: 'BG-HCM-HN-01',
  applicableUnits: ['ABC Logistics'],
  effectiveDate: '2025-06-17',
  expiryDate: '',
  serviceType: 'Vận chuyển nội địa',
  services: [
    {
      key: '1',
      name: 'Vận chuyển HCM → HN',
      unit: 'chuyến',
      price: 15000000,
      vat: '10%',
    },
    {
      key: '2',
      name: '<PERSON><PERSON> bốc xếp',
      unit: 'lần',
      price: 2000000,
      vat: '10%',
    },
    {
      key: '3',
      name: '<PERSON><PERSON>u kho (ngoài giờ)',
      unit: 'ngày',
      price: 50000,
      vat: '10%',
    },
  ],
}

export const QuotationTemplateEdit = () => {
  const [form] = Form.useForm()

  const handleFinish = (values: any) => {
    console.log('Form Values:', values)
  }

  const columns = [
    {
      title: 'STT',
      render: (_: any, __: any, index: number) => index + 1,
      width: 60,
    },
    {
      title: 'Tên dịch vụ',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'ĐVT',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) =>
        price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
    },
    {
      title: 'VAT',
      dataIndex: 'vat',
      key: 'vat',
    },
  ]

  return (
    <Card title="Chỉnh sửa template báo giá">
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          ...mockDetail,
          applicableUnits: mockDetail.applicableUnits,
          dateRange: [
            dayjs(mockDetail.effectiveDate),
            mockDetail.expiryDate ? dayjs(mockDetail.expiryDate) : null,
          ],
        }}
        onFinish={handleFinish}
      >
        <Form.Item label="Tên template" name="templateName" rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Form.Item label="Mã template" name="templateCode" rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Form.Item label="Đơn vị áp dụng" name="applicableUnits" rules={[{ required: true }]}>
          <Select mode="tags" placeholder="Nhập đơn vị áp dụng" />
        </Form.Item>

        <Form.Item label="Khoảng thời gian hiệu lực" name="dateRange">
          <RangePicker format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item label="Loại dịch vụ" name="serviceType" rules={[{ required: true }]}>
          <Input />
        </Form.Item>

        <Title level={5} style={{ marginTop: 24 }}>
          Danh sách dịch vụ trong báo giá
        </Title> 

        <Button type="primary" style={{ marginBottom: 16 }} icon={<PlusOutlined />}>Thêm dòng dịch vụ</Button>
        <Table
          dataSource={mockDetail.services}
          columns={columns}
          pagination={false}
          bordered
          size="small"
          rowKey="key"
        />

        <Form.Item style={{ marginTop: 24 }}>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
              Lưu
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  )
}

import { FC } from 'react'
import BaseView from '~/components/BaseView'
import BaseText from '~/components/BaseText'
import { Row, Col, Card, Statistic } from 'antd'
import { Bar, Line, Doughnut, Pie } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
)

// Bar Chart Data - Customer Classification
const barData = {
  labels: [
    `<PERSON><PERSON>a định danh`,
    `<PERSON><PERSON><PERSON><PERSON> năng`,
    `<PERSON><PERSON> điều kiện bán hàng`,
    `<PERSON><PERSON> hội`,
    `<PERSON><PERSON><PERSON> thứ<PERSON>`,
    `<PERSON><PERSON><PERSON> sóc và phát triển`,
    `<PERSON>hông thành công`
  ],
  datasets: [
    {
      label: 'Số lượng',
      data: [1000, 1600, 2000, 1300, 1400 , 1000, 2000],
      backgroundColor: [
        'rgba(180, 205, 230, 1)',
        'rgba(140, 180, 220, 1)',
        'rgba(100, 155, 210, 1)',
        'rgba(100, 155, 210, 1)',
        'rgba(60, 130, 200, 1)',
        'rgba(30, 100, 180, 1)',
        'rgba(10, 70, 150, 1)'
      ],
      borderRadius: 8,
      borderSkipped: false,
      barPercentage: 0.7,
      categoryPercentage: 0.7
    }
  ]
}

const barOptions = {
  indexAxis: 'y' as const,
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false },
    tooltip: {
      callbacks: {
        label: (ctx: any) =>
          `${ctx.parsed.x} khách hàng (${((ctx.parsed.x / 10000) * 100).toFixed(0)}%)`
      }
    },
    title: { display: false }
  },
  scales: {
    x: {
      min: 0,
      max: 2500,
      ticks: {
        stepSize: 500,
        font: { size: 12 }
      },
      grid: { display: false }
    },
    y: {
      ticks: {
        font: { size: 18, weight: 'bold' as const }
      },
      grid: { display: false }
    }
  },
  elements: {
    bar: {
      borderWidth: 0
    }
  }
}

// Line Chart Data - Customer Growth
const lineData = {
  labels: ['2021', '2022', '2023', '2024', '2025'],
  datasets: [
    {
      label: 'Tổng khách hàng',
      data: [3000, 6500, 8000, 9800, 10000],
      borderColor: 'rgba(255, 99, 132, 1)',
      backgroundColor: 'rgba(255, 99, 132, 0.1)',
      pointBackgroundColor: 'rgba(255, 99, 132, 1)',
      pointBorderColor: '#fff',
      pointRadius: 6,
      pointHoverRadius: 8,
      fill: true,
      tension: 0.4
    }
  ]
}

const growthLabels = ['+27.45%', '+23.08%', '+22.5%', '+2.04%']

const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false },
    tooltip: {
      callbacks: {
        label: (ctx: any) =>
          `${ctx.parsed.y} khách hàng${
            ctx.dataIndex > 0 ? ' (' + growthLabels[ctx.dataIndex - 1] + ')' : ''
          }`
      }
    },
    title: { display: false }
  },
  scales: {
    x: {
      ticks: { font: { size: 12 } },
      grid: { display: false }
    },
    y: {
      min: 0,
      max: 12000,
      ticks: { stepSize: 2000, font: { size: 12 } },
      grid: { display: false }
    }
  },
  elements: {
    line: { borderWidth: 3 },
    point: { borderWidth: 2 }
  }
}

export const CustomerSummaryReport: FC = () => {
  return (
    <BaseView>
      <Row gutter={[24, 24]}>
        {/* Customer Classification Bar Chart */}
        <Col xs={24} lg={12}>
          <Card title='Phân loại khách hàng' style={{ height: '100%' }}>
            <div style={{ height: 400, position: 'relative' }}>
              <Bar
                data={{
                  ...barData
                  // No change to data, just ensure vertical orientation
                }}
                options={{
                  ...barOptions,
                  indexAxis: 'x', // Chuyển sang cột dọc
                  scales: {
                    x: {
                      min: 0,
                      max: undefined,
                      ticks: {
                        font: { size: 12, weight: 'bold' as const }
                      },
                      grid: { display: false }
                    },
                    y: {
                      min: 0,
                      max: 2500,
                      ticks: {
                        stepSize: 500,
                        font: { size: 12 }
                      },
                      grid: { display: false }
                    }
                  }
                }}
                style={{
                  marginTop: 30
                }}
              />
              {/* Custom labels on top of each bar */}
              <div
                style={{
                  position: 'absolute',
                  top: -70,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'none',
                  display: 'flex',
                  justifyContent: 'space-around',
                  padding: '20px 30px 0 10px',
                  marginLeft: 30
                }}>
                {barData.datasets[0].data.map((val, idx) => (
                  <div
                    key={idx}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      fontWeight: 600,
                      fontSize: 14
                    }}>
                    <span style={{ color: '#222', marginBottom: 2 }}>{val.toLocaleString()}</span>
                    <span style={{ color: '#888', fontSize: 12 }}>
                      ({Math.round((val / 8900) * 100)}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>

        {/* Customer Growth Line Chart */}
        <Col xs={24} lg={12}>
          <Card title='Tăng trưởng khách hàng theo năm' style={{ height: '100%' }}>
            <div style={{ height: 400 }}>
              <Line data={lineData} options={lineOptions} />
              {/* Custom value + growth labels */}
              <div
                style={{
                  position: 'absolute',
                  top: 60,
                  left: 0,
                  width: '100%',
                  height: 'calc(100% - 120px)',
                  pointerEvents: 'none',
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'flex-end',
                  justifyContent: 'space-between',
                  padding: '0 24px 32px 24px'
                }}>
                {lineData.datasets[0].data.map((val, idx) => (
                  <div
                    key={idx}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      marginBottom: 0
                    }}>
                    <span style={{ color: 'red', fontWeight: 700, fontSize: 14 }}>
                      {val.toLocaleString()}
                    </span>
                    {idx > 0 && (
                      <span style={{ color: 'red', fontSize: 12, fontWeight: 600 }}>
                        {growthLabels[idx - 1]}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </BaseView>
  )
}

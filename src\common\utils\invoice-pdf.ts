// src/utils/invoice-pdf.ts
import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import "../fonts/dejavu-sans-latin-400-italic-normal";

export const exportInvoicePDF = () => {
  const doc = new jsPDF()
  // Dùng font tiếng Việt
  doc.setFont('DejaVuSans')
  doc.setFontSize(16)
  doc.text('HÓA ĐƠN GIÁ TRỊ GIA TĂNG', 105, 15, { align: 'center' })

  // Thông tin người bán / khách hàng
  doc.setFontSize(11)
  doc.text('CÔNG TY TNHH ABC LOGISTICS', 14, 25)
  doc.text('Mã số thuế: 0312345678', 14, 30)
  doc.text('Địa chỉ: 123 Đường Lê Văn <PERSON>, Q.3, TP.HCM', 14, 35)

  doc.text('Khách hàng: CÔNG TY XYZ', 14, 45)
  doc.text('Địa chỉ: Số 5 Tr<PERSON><PERSON>, <PERSON><PERSON><PERSON>', 14, 50)
  doc.text('<PERSON><PERSON> số thuế: 0101122334', 14, 55)

  // Bảng dịch vụ
  autoTable(doc, {
    startY: 65,
    head: [['STT', 'Tên dịch vụ', 'Số lượng', 'Đơn giá', 'VAT (%)', 'Thành tiền']],
    body: [
      ['1', 'Phí vận chuyển', '1', '20.000', '10', '22.000'],
      ['2', 'Phí bốc xếp', '1', '5.000', '10', '5.500'],
      ['3', 'Phụ phí cầu đường', '1', '3.000', '10', '3.300'],
    ],
    styles: {
      font: 'DejaVuSans',
      fontSize: 10,
    },
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: [255, 255, 255],
    },
  })

  const finalY = (doc as any).lastAutoTable.finalY + 10

  // Tổng tiền
  doc.setFontSize(11)
  doc.text('Tổng tiền hàng (chưa VAT): 28.000', 140, finalY)
  doc.text('Thuế GTGT (10%): 2.800', 140, finalY + 5)
  doc.text('Tổng thanh toán: 30.800', 140, finalY + 10)

  // Viết bằng chữ
  doc.text('Số tiền bằng chữ: Ba mươi nghìn tám trăm đồng', 14, finalY + 20)

  // Chữ ký
  doc.setFontSize(11)
  doc.text('Người bán hàng', 20, finalY + 45)
  doc.text('Người mua hàng', 140, finalY + 45)

  doc.setTextColor(0, 128, 0)
  doc.text('✔ Chữ ký số hợp lệ', 20, finalY + 50)
  doc.setTextColor(0, 0, 0)

  doc.text('Ký bởi: NGUYEN VAN TSK', 20, finalY + 55)
  doc.text('Ký ngày: 17/06/2025', 20, finalY + 60)

  // Xuất file
  doc.save('hoa-don.pdf')
}

export interface ICustomerEvaluationResponse {
  data: ICustomerEvaluation[]
  total: number
}

export interface ICustomerEvaluation {
  id: string
  code: string
  name: string
  customerCount: number
  status: boolean
  companyName: string
  createdBy: string
  createdAt: string
  source: string
  companyCode: string
  region: string
  address: string
  city: string
  district: string
  ward: string
  email: string
  phone: string
  position: string
  firstAccessTime: string
  accessCount30Days: number
  evaluationCriteria: ITreeNode[]
}

export interface ITreeNode {
  key: string
  title: string
  value: string
  children?: ITreeNode[]
}

export interface ICustomerContactNotIdentifyFilter {
  pageIndex: number
  pageSize: number
  search?: string
}

export interface ICustomerEvaluationResponse {
  data: ICustomerEvaluation[]
  total: number
}

import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  Card
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect } from 'react'

const { Option } = Select

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateCustomerCriteriaModal = ({ open, onClose, onSuccess }: CreateProductModalProps) => {
  const [form] = useForm()
  useEffect(() => {
    form.setFieldsValue({
      id: null,
      name: null,
      description: null,
      status: null
    })
  }, [open])

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Tên' name='name'>
                <Input placeholder='Nhập tên tiêu chí' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Mô tả' name='description'>
                <Input placeholder='Nhập mô tả' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Trạng thái' name='status'>
                <Select placeholder='Chọn trạng thái'>
                  <Option value='Hoạt động'>Hoạt động</Option>
                  <Option value='Không hoạt động'>Không hoạt động</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <div
          style={{
            textAlign: 'center',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </div>
      </Card>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo tiêu chí đánh giá khách hàng'
      description='Thêm mới tiêu chí khách hàng'
      childrenBody={modalContent}
    />
  )
}

export default CreateCustomerCriteriaModal

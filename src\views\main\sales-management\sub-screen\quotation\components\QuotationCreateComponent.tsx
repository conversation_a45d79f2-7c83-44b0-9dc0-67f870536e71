import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  Card
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useState } from 'react'
import { EProduct } from '~/common/enums/NSProduct'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import { Collapse } from 'antd/lib'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

const { Option } = Select
const { TextArea } = Input

interface CreateQuotationModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateQuotationModal = ({
  open,
  onClose,
  onSuccess
}: CreateQuotationModalProps) => {
  const [form] = useForm()
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const { mutateAsync: createProduct, isPending } = useCreateProduct()
  const { t } = useTranslation()

  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } = useUploadSingle()

  const handleSubmit = (values: any) => {
    console.log('Form values:', values)
  }

  const handleSave = async (values: CreateProductReq) => {
    if (!values) return

    const body = {
      ...values,
      images: fileList.map((file) => file.url)
    }

    try {
      await createProduct(body)
      onClose()
      onSuccess?.()
      // Reset form and file list
      form.resetFields()
      setFileList([])
    } catch (error) {
      toastService.error('Tạo product thất bại')
    }
  }

  // Handle image upload
  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!')
      return false
    }

    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      message.error('Hình ảnh phải nhỏ hơn 5MB!')
      return false
    }

    return true
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  const columns = [
    {
      title: t('quotation:quotation.columns_item.itemLine'),
      dataIndex: 'itemLine',
      width: 80
    },
    {
      title: t('quotation:quotation.columns_item.category'),
      dataIndex: 'category'
    },
    {
      title: t('quotation:quotation.columns_item.material'),
      dataIndex: 'material'
    },
    {
      title: t('quotation:quotation.columns_item.shortText'),
      dataIndex: 'shortText'
    },
    {
      title: t('quotation:quotation.columns_item.quantity'),
      dataIndex: 'quantity'
    },
    {
      title: t('quotation:quotation.columns_item.deliveryDate'),
      dataIndex: 'deliveryDate'
    },
    {
      title: t('quotation:quotation.columns_item.plant'),
      dataIndex: 'plant'
    },
    {
      title: t('quotation:quotation.columns_item.unit'),
      dataIndex: 'unit'
    },
    {
      title: t('quotation:quotation.columns_item.materialGroup'),
      dataIndex: 'materialGroup'
    },
    {
      title: t('quotation:quotation.columns_item.actions'),
      dataIndex: 'actions'
    }
  ]

  const modalContent = (
    <Card>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Form.Item>
          <Button type='primary' htmlType='submit'>
            {t('button.title.save')}
          </Button>
        </Form.Item>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label={t('quotation:quotation.header.customer')}
              name='customer'
              rules={[{ required: true }]}>
              <Select placeholder={t('quotation:quotation.place_holder.customer')}>
                <Option value='1'>Công ty TNHH ABC</Option>
                <Option value='2'>Công ty TNHH XYZ</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.fromAddress')} name='fromAddress'>
              <Input placeholder={t('quotation:quotation.place_holder.fromAddress')} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.toAddress')} name='toAddress'>
              <Input placeholder={t('quotation:quotation.place_holder.toAddress')} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.goodsType')} name='goodsType'>
              <Select placeholder={t('quotation:quotation.place_holder.goodsType')}>
                <Option value='hangKho'>Hàng khô</Option>
                <Option value='hangLanh'>Hàng lạnh</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.weight')} name='weight'>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.packageCount')} name='packageCount'>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.transportMethod')} name='transportMethod'>
              <Select placeholder={t('quotation:quotation.place_holder.transportMethod')}>
                <Option value='duongBo'>Đường bộ</Option>
                <Option value='duongBien'>Đường biển</Option>
                <Option value='hangKhong'>Hàng không</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.baseFee')} name='baseFee'>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.packingFee')} name='packingFee'>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.insuranceFee')} name='insuranceFee'>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.storageFee')} name='storageFee'>
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.totalFee')} name='totalFee'>
              <InputNumber style={{ width: '100%' }} min={0} disabled />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.deliveryTime')} name='deliveryTime'>
              <Input placeholder={t('quotation:quotation.place_holder.deliveryTime')} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.paymentTerm')} name='paymentTerm'>
              <Select placeholder='Chọn điều kiện'>
                <Option value='thanhToanTruoc'>Thanh toán trước</Option>
                <Option value='thanhToanSau'>Thanh toán sau 14 ngày</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label={t('quotation:quotation.header.note')} name='paymentTerm'>
              <Input />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Collapse>
        <Collapse.Panel header={t('quotation:quotation.header.itemInfo')} key='1'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label='External MatGroup'
                name='externalMatGroup'
                required>
                <Select placeholder='Chọn MatGroup'>
                  <Option value=''>--All--</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={t('quotation:quotation.header.referenceSource')}
                name='referenceSource'
                required>
                <Select placeholder={t('quotation:quotation.place_holder.referenceSource')}>
                  <Option value=''>--All--</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <BaseTable columns={columns} data={[]} total={0} isLoading={false} />
        </Collapse.Panel>
      </Collapse>
    </Card>
  )

  return (
    <BaseModal
      open={open}
      title={t('quotation:quotation.create')}
      description={t('quotation:quotation.header.description')}
      onClose={onClose}
      width={2000}
      childrenBody={modalContent}
    />
  )
}

export default CreateQuotationModal

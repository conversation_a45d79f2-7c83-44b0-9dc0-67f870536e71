import {
  EyeOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CopyOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined
} from '@ant-design/icons'
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  message,
  Image,
  Empty,
  Form,
  StepProps,
  Steps,
  DatePicker,
  Input,
  Radio,
  Select,
  Divider
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom, formatMoneyVND } from '~/common/helper/helper'
import { IProduct } from '~/dto/product.dto'
import { EProduct, NSProduct } from '~/common/enums/NSProduct'
import { useDetailProduct } from '~/hooks/product/useDetailProduct'
import { ICustomerContact } from '~/dto/customer_contact.dto'
import TextArea from 'antd/es/input/TextArea'
import { useEffect, useState } from 'react'

const { Title, Paragraph, Text } = Typography

interface DetailButtonProps {
  data: ICustomerContact
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  let [current, setCurrent] = useState(0)

  useEffect(() => {
    switch (data.status) {
      case 'NEW':
        setCurrent(0)
        break
      case 'PROCESSING':
        setCurrent(1)
        break
      case 'COMPLETED':
        setCurrent(2)
        break
      default:
        setCurrent(0)
        break
    }
  }, [open])

  const modalContent = (
    <div>
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Steps
          current={current}
          labelPlacement='vertical'
          style={{ width: '100%' }}
          items={[
            { title: 'Mới tạo' },
            { title: 'Đang xử lý' },
            { title: 'Hoàn thành' }
          ]}
        />

        <Row gutter={16}>
          {/* Trạng thái & phân loại */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text>
            <br />
            <Text>{data.status}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phân loại chuyến thăm:</Text>
            <br />
            <Text>{data.type}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tiêu đề:</Text>
            <br />
            <Text>{data.title}</Text>
          </Col>

          {/* Địa điểm */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Địa điểm ghé thăm:</Text>
            <br />
            <Text>{data.visitLocation}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Yêu cầu checkin:</Text>
            <br />
            <Text>{data.checkInDate ? 'Có' : 'Không'}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Toạ độ lat/lng:</Text>
            <br />
            <Text>13.9748344 / 108.0037406</Text>
          </Col>

          {/* Địa lý hành chính */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Khu vực:</Text>
            <br />
            <Text>Khu vực Tây Nguyên - Central Highlands</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tỉnh/Thành phố:</Text>
            <br />
            <Text>Gia Lai</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Quận/Huyện:</Text>
            <br />
            <Text>Thành phố Pleiku</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phường/Xã:</Text>
            <br />
            <Text>Phường Hội Thương</Text>
          </Col>

          {/* Phân công */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>NV theo dõi/giám sát:</Text>
            <br />
            <Text>{data.supervisor}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phân công cho:</Text>
            <br />
            <Text>Cá nhân</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>NV được phân công:</Text>
            <br />
            <Text>{data.assignedEmployee}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phòng ban phân công:</Text>
            <br />
            <Text>Tư vấn vật liệu - TT DVKH</Text>
          </Col>

          {/* Mô tả & Kết quả */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Mô tả:</Text>
            <br />
            <Text>{data.description}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Kết quả (mô tả):</Text>
            <br />
            <Text>{data.messageStatus}</Text>
          </Col>

          {/* Ngày tháng */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày dự kiến:</Text>
            <br />
            <Text>{data.dueDate}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày thực tế:</Text>
            <br />
            <Text>{data.actualDate}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Người tạo:</Text>
            <br />
            <Text>{data.supervisor}</Text>
          </Col>

          {/* Mô tả chuyến thăm */}
          <Col span={24} style={{ marginBottom: 16 }}>
            <Text strong>Mô tả chuyến thăm:</Text>
            <br />
            <Text>
              Đây là mô tả chi tiết chuyến thăm: bao gồm nội dung trao đổi, tình
              trạng khách hàng, đề xuất vật tư, đánh giá tình hình thực tế tại
              địa điểm, và các thông tin bổ sung cần lưu lại.
            </Text>
          </Col>

          <Divider />

          {/* Khách hàng */}
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Khách hàng:</Text>
            <br />
            <Text>{data.customerCode}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>NV kinh doanh:</Text>
            <br />
            <Text>{data.assignedEmployee}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Chi nhánh:</Text>
            <br />
            <Text>{data.sapCode}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Địa chỉ:</Text>
            <br />
            <Text>{data.address}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Liên hệ:</Text>
            <br />
            <Text>100350303 | Anh Thái</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>SĐT liên hệ:</Text>
            <br />
            <Text>0966540571</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Email:</Text>
            <br />
            <Text><EMAIL></Text>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết thăm hỏi khách hàng'
        description='Thông tin chi tiết Chi tiết thăm hỏi khách hàng'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton

import { useInfiniteQuery } from '@tanstack/react-query'
import { IProduct } from '~/dto/product.dto'
import { rootApiService } from '~/services/@common'
import { endpoints_customer } from '~/services/endpoints'
import { PageResponse } from '~/@ui/GridControl/models'

interface ListCustomerParams {
  code?: string
  name?: string
  status?: string
  pageIndex?: number
  pageSize?: number
}

export const useListCustomer = (params: ListCustomerParams) => {
  const { data, isLoading, refetch } = useInfiniteQuery<any, Error>({
    queryKey: [
      endpoints_customer.list,
      {
        ...params,
        pageSize: params.pageSize,
        pageIndex: params.pageIndex
      }
    ],
    queryFn: () =>
      rootApiService.get(endpoints_customer.list, {
        ...params,
        pageSize: params.pageSize || 10,
        pageIndex: params.pageIndex || 1
      }),
    getNextPageParam: (lastPage, allPages) =>
      lastPage.data.length > 0 ? allPages.length + 1 : undefined,
    initialPageParam: 1
  })

  const formatData = data?.pages.flatMap((page) => page.data) ?? []
  const total = data?.pages[0]?.total ?? 0
  return {
    data: formatData,
    isLoading: isLoading,
    refetch,
    total: total
  }
}

import React from 'react'
import {
  Form,
  Input,
  Select,
  Radio,
  Button,
  Upload,
  Typography,
  Card,
  Row,
  Col
} from 'antd'
import ReactQuill from 'react-quill'
import 'react-quill/dist/quill.snow.css'
import { RollbackOutlined, SaveOutlined } from '@ant-design/icons'
import Column from 'antd/es/table/Column'

const { Option } = Select
const { Title } = Typography

export const ContentEditComponent = () => {
  const [form] = Form.useForm()

  const handleFinish = (values: any) => {
    console.log('Form values:', values)
  }

  return (
    <div>
      <Card
        title='SỬA NỘI DUNG'
        style={{ width: '100%', height: '85vh', overflow: 'auto' }}
        extra={
          <Button type='primary' icon={<RollbackOutlined />}>
            Lịch sử
          </Button>
        }>
        <Form
          form={form}
          layout='vertical'
          onFinish={handleFinish}
          initialValues={{ emailType: 'Có sẵn', status: 'active' }}>
          <Form.Item label='Mã nội dung' name='contentCode'>
            <Input disabled placeholder='1225' />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label='Tên nội dung'
                name='contentName'
                rules={[{ required: true }]}>
                <Input placeholder='🌈🌈 Bộ sưu tập mới của Công Ty ABC 🌈🌈' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Gửi từ email' name='fromEmail'>
                <Input placeholder='<EMAIL>' />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label='Loại Email' name='emailType'>
                <Radio.Group>
                  <Radio value='Có sẵn'>Có sẵn</Radio>
                  <Radio value='Cá nhân'>Cá nhân</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Tên người gửi' name='senderName'>
                <Input placeholder='An Cường' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Công ty' name='company'>
                <Select defaultValue='1000'>
                  <Option value='1000'>
                    1000 | Công Ty Cổ Phần Gỗ An Cường
                  </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Chi nhánh' name='branch'>
                <Select defaultValue='1000'>
                  <Option value='1000'>
                    1000 | Công Ty Cổ Phần Gỗ An Cường
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label='Tiêu đề'
            name='subject'
            rules={[{ required: true }]}>
            <Input placeholder='🌈🌈 Bộ sưu tập mới của Công Ty ABC 🌈🌈' />
          </Form.Item>

          <Form.Item label='Trạng thái' name='status'>
            <Radio.Group>
              <Radio value='active'>Đang sử dụng</Radio>
              <Radio value='inactive'>Ngừng sử dụng</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item label='Tham số cho nội dung gửi' name='params'>
            <Input placeholder='##FullName##' disabled />
          </Form.Item>

          <Form.Item label='Upload hình ảnh'>
            <Upload beforeUpload={() => false} maxCount={1}>
              <Button>Chọn tệp</Button>
            </Upload>
          </Form.Item>

          <Form.Item label='Nội dung' name='content'>
            <ReactQuill theme='snow' style={{ height: 250 }} />
          </Form.Item>

          <Row>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Form.Item>
                <Button
                  type='primary'
                  htmlType='submit'
                  icon={<SaveOutlined />}
                  style={{ marginTop: 24, alignItems: 'center' }}>
                  Lưu
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  )
}

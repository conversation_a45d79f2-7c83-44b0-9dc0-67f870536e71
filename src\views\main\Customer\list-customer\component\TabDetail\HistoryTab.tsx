import { FC } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import { DeleteOutlined, EditOutlined, UserOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import BaseTable from '~/components/BaseTable'
import { EProduct } from '~/common/enums/NSProduct'
import { IGift } from '~/dto/gift.dto'
import { useGift } from '~/hooks/gift/useGift'
import { useTranslation } from 'react-i18next'

interface IFilterLoyalty {
  programName: string
  phoneNumber: string
  customerCode: string
  rewardExchange: string
  productName: string
  version: string
  status: string
  pageIndex: number
  pageSize: number
}

interface IOperationHistory {
  id: string
  operationType: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW'
  operationName: string
  operator: string
  operatorRole: string
  targetObject: string
  oldValue?: string
  newValue?: string
  operationTime: string
  ipAddress: string
  deviceInfo: string
  status: 'SUCCESS' | 'FAILED'
  note?: string
}

const fakeData: IOperationHistory[] = [
  {
    id: '1',
    operationType: 'CREATE',
    operationName: 'Tạo khách hàng mới',
    operator: 'Nguyễn Văn A',
    operatorRole: 'Admin',
    targetObject: 'Customer',
    newValue: '{"name": "Trần Văn B", "phone": "0987654321"}',
    operationTime: '2024-03-20T10:30:00',
    ipAddress: '***********',
    deviceInfo: 'Chrome/Windows',
    status: 'SUCCESS',
    note: 'Tạo khách hàng thành công'
  },
  {
    id: '2',
    operationType: 'UPDATE',
    operationName: 'Cập nhật thông tin khách hàng',
    operator: 'Lê Thị C',
    operatorRole: 'Staff',
    targetObject: 'Customer',
    oldValue: '{"address": "Hà Nội"}',
    newValue: '{"address": "Hồ Chí Minh"}',
    operationTime: '2024-03-20T11:15:00',
    ipAddress: '***********',
    deviceInfo: 'Safari/MacOS',
    status: 'SUCCESS',
    note: 'Cập nhật địa chỉ'
  },
  {
    id: '3',
    operationType: 'DELETE',
    operationName: 'Xóa khách hàng',
    operator: 'Phạm Văn D',
    operatorRole: 'Manager',
    targetObject: 'Customer',
    oldValue: '{"id": "CUS123", "name": "Nguyễn Văn E"}',
    operationTime: '2024-03-20T14:20:00',
    ipAddress: '***********',
    deviceInfo: 'Firefox/Windows',
    status: 'FAILED',
    note: 'Không có quyền xóa'
  }
]

type IProps = {}

export const HistoryTab: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  const { data, isLoading } = useGift()

  const columns: ColumnsType<IOperationHistory> = [
    {
      title: t('customer:customer.customer_detail.history_tab.columns.stt'),
      key: 'stt',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer.customer_detail.history_tab.columns.time'),
      dataIndex: 'operationTime',
      key: 'operationTime',
      width: 150,
      render: (value: string) => new Date(value).toLocaleString('vi-VN')
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.operationType'
      ),
      dataIndex: 'operationType',
      key: 'operationType',
      width: 120,
      render: (value: string) => {
        const colors = {
          CREATE: 'green',
          UPDATE: 'blue',
          DELETE: 'red',
          VIEW: 'gray'
        }
        return <Tag color={colors[value as keyof typeof colors]}>{value}</Tag>
      }
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.operationName'
      ),
      dataIndex: 'operationName',
      key: 'operationName',
      width: 200
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.operator'
      ),
      dataIndex: 'operator',
      key: 'operator',
      width: 150
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.operatorRole'
      ),
      dataIndex: 'operatorRole',
      key: 'operatorRole',
      width: 120
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.targetObject'
      ),
      dataIndex: 'targetObject',
      key: 'targetObject',
      width: 120
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.oldValue'
      ),
      dataIndex: 'oldValue',
      key: 'oldValue',
      width: 200,
      render: (value: string) => value || '-'
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.newValue'
      ),
      dataIndex: 'newValue',
      key: 'newValue',
      width: 200,
      render: (value: string) => value || '-'
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.ipAddress'
      ),
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120
    },
    {
      title: t(
        'customer:customer.customer_detail.history_tab.columns.deviceInfo'
      ),
      dataIndex: 'deviceInfo',
      key: 'deviceInfo',
      width: 150
    },
    {
      title: t('customer:customer.customer_detail.history_tab.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (value: string) => (
        <Tag color={value === 'SUCCESS' ? 'green' : 'red'}>{value}</Tag>
      )
    },
    {
      title: t('customer:customer.customer_detail.history_tab.columns.note'),
      dataIndex: 'note',
      key: 'note',
      width: 200,
      render: (value: string) => value || '-'
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={fakeData}
            total={fakeData.length}
            isLoading={false}
            scroll={{ x: 2000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

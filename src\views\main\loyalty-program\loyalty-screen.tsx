import { useState, FC } from 'react'
import { Col, Row, Tag, Space } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { useLoyalty } from '~/hooks/loyalty/useLoyalty'
import {
  IFilterLoyalty,
  ILoyalty,
  LoyaltyStatusConfig
} from '~/dto/loyalty.dto'
import FilterLoyalty from './components-loyalty/FilterLoyalty'
import { LoyaltyButton } from './components-loyalty/LoyaltyButton'
import { formatMoneyVND } from '~/common/helper/helper'

type IProps = {}

export const LoyaltyScreen: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<IFilterLoyalty>({
    customerCode: '',
    customerName: '',
    phoneNumber: '',
    programCode: '',
    programName: '',
    status: '',
    pageIndex: 1,
    pageSize: 10
  })

  const { data, isLoading } = useLoyalty()

  const handleFilter = (values: IFilterLoyalty) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      customerCode: '',
      customerName: '',
      phoneNumber: '',
      programCode: '',
      programName: '',
      status: '',
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleExchangeReward = (giftId: string, quantity: number) => {
    // TODO: Implement exchange reward functionality
    console.log('Exchange reward:', { giftId, quantity })
  }

  const columns: ColumnsType<ILoyalty> = [
    {
      title: t('loyalty:loyalty_screen.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('loyalty:loyalty_screen.columns.customerCode'),
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      align: 'center'
    },
    {
      title: t('loyalty:loyalty_screen.columns.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      align: 'center'
    },
    {
      title: t('loyalty:loyalty_screen.columns.phoneNumber'),
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      width: 120,
      align: 'center'
    },
    {
      title: t('loyalty:loyalty_screen.columns.programCode'),
      dataIndex: 'programCode',
      key: 'programCode',
      width: 120,
      align: 'center'
    },
    {
      title: t('loyalty:loyalty_screen.columns.programName'),
      dataIndex: 'programName',
      key: 'programName',
      width: 200,
      align: 'center'
    },
    {
      title: t('loyalty:loyalty_screen.columns.totalAccumulatedMoney'),
      dataIndex: 'totalAccumulatedMoney',
      key: 'totalAccumulatedMoney',
      width: 150,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:loyalty_screen.columns.exchangedMoney'),
      dataIndex: 'exchangedMoney',
      key: 'exchangedMoney',
      width: 150,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:loyalty_screen.columns.remainingMoney'),
      dataIndex: 'remainingMoney',
      key: 'remainingMoney',
      width: 150,
      align: 'center',
      render: (value: number) => formatMoneyVND(value)
    },
    {
      title: t('loyalty:loyalty_screen.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: string) => {
        const statusConfig =
          LoyaltyStatusConfig[value as keyof typeof LoyaltyStatusConfig]
        return (
          <Tag
            color={statusConfig?.color}
            style={{
              fontSize: '14px',
              padding: '4px 12px',
              width: '100%',
              textAlign: 'center'
            }}>
            {statusConfig?.label || value?.toUpperCase() || ''}
          </Tag>
        )
      }
    },
    {
      title: t('loyalty:loyalty_screen.columns.action'),
      key: 'action',
      width: 80,
      fixed: 'right',
      align: 'center',
      render: (_, record) => {
        return (
          <Space>
            <LoyaltyButton record={record} onExchange={handleExchangeReward} />
          </Space>
        )
      }
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterLoyalty
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={isLoading}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={data?.total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 1300 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

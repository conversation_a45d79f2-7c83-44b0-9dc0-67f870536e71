import { EyeOutlined } from '@ant-design/icons'
import { Card, Typography, Row, Col, Tag } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom } from '~/common/helper/helper'
import { useModal } from '~/hooks/useModal'
import { useDetailCatalog } from '~/hooks/catalog/useDetailCatalog'
import { NSCatalog } from '~/common/enums/NSCatalog'
import { useEffect } from 'react'

const { Text } = Typography

const DetailButton = ({ id }) => {
  const { open, openModal, closeModal } = useModal()
  const { data, refetch } = useDetailCatalog(id, false)

  useEffect(() => {
    if (open) {
      refetch()
    }
  }, [open, refetch])

  const modalContent = data && (
    <div>
      {/* Product Overview Card */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Row gutter={16}>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Mã sản phẩm:</Text>
            <br />
            <Text>{data.code}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tên sản phẩm:</Text>
            <br />
            <Text>{data.name}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Loại sản phẩm:</Text>
            <br />
            <Text>{NSCatalog.EType[data.type].name}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text>
            <br />
            <Tag color={data.status === NSCatalog.EStatus.ACTIVE.code ? 'green' : 'red'}>
              {NSCatalog.EStatus[data.status].name}
            </Tag>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày tạo:</Text>
            <br />
            <Text>{formatDateCustom(data.createdDate, 'DD/MM/YYYY')}</Text>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EyeOutlined />}
        onClick={openModal}
        type='primary'
        tooltip='Xem chi tiết'
        children='Xem chi tiết'
      />
      <BaseModal
        open={open}
        title='Chi tiết sản phẩm'
        description='Thông tin chi tiết sản phẩm'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton

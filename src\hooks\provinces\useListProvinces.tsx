import { useQuery } from "@tanstack/react-query";
import { rootApiService } from "~/services/@common";
import { endpoints_provinces } from "~/services/endpoints";

export const useListProvinces = () => {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [endpoints_provinces.list],
    queryFn: () => rootApiService.get(endpoints_provinces.list),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const provinces: any = data || [];

  return {
    data: provinces,
    isLoading,
    refetch,
  };
};

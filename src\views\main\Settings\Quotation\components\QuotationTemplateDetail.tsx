import React from 'react'
import { Card, Descriptions, Table, Typography, Tag } from 'antd'

const { Title } = Typography

interface QuotationService {
  key: string
  name: string
  unit: string
  price: number
  vat: string
}

interface QuotationTemplate {
  templateName: string
  templateCode: string
  applicableUnits: string[]
  effectiveDate: string
  expiryDate?: string
  serviceType: string
  services: QuotationService[]
}

const mockDetail: QuotationTemplate = {
  templateName: 'Template HCM → HN',
  templateCode: 'BG-HCM-HN-01',
  applicableUnits: ['ABC Logistics'],
  effectiveDate: '2025-06-17',
  expiryDate: '', // bỏ trống nếu không giới hạn
  serviceType: 'Vận chuyển nội địa',
  services: [
    {
      key: '1',
      name: 'Vận chuyển HCM → HN',
      unit: 'chuyến',
      price: 15000000,
      vat: '10%',
    },
    {
      key: '2',
      name: '<PERSON><PERSON> bố<PERSON> xếp',
      unit: 'lần',
      price: 2000000,
      vat: '10%',
    },
    {
      key: '3',
      name: '<PERSON><PERSON><PERSON> k<PERSON> (ngoài giờ)',
      unit: 'ngày',
      price: 50000,
      vat: '10%',
    },
  ],
}

export const QuotationTemplateDetail = () => {
  const columns = [
    {
      title: 'STT',
      key: 'index',
      render: (_: any, __: any, index: number) => index + 1,
      width: 60,
    },
    {
      title: 'Tên dịch vụ',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'ĐVT',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) =>
        price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
    },
    {
      title: 'VAT',
      dataIndex: 'vat',
      key: 'vat',
      width: 80,
    },
  ]

  return (
    <Card>
      <Title level={4}>Chi tiết báo giá mẫu</Title>
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="Tên template">{mockDetail.templateName}</Descriptions.Item>
        <Descriptions.Item label="Mã template">{mockDetail.templateCode}</Descriptions.Item>
        <Descriptions.Item label="Đơn vị áp dụng" span={2}>
          {mockDetail.applicableUnits.map((unit, idx) => (
            <Tag color="blue" key={idx}>
              {unit}
            </Tag>
          ))}
        </Descriptions.Item>
        <Descriptions.Item label="Ngày hiệu lực">{mockDetail.effectiveDate}</Descriptions.Item>
        <Descriptions.Item label="Ngày hết hiệu lực">
          {mockDetail.expiryDate || 'Không giới hạn'}
        </Descriptions.Item>
        <Descriptions.Item label="Loại dịch vụ" span={2}>
          {mockDetail.serviceType}
        </Descriptions.Item>
      </Descriptions>

      <Title level={5} style={{ marginTop: 24 }}>
        Danh sách dịch vụ trong báo giá
      </Title>
      <Table
        dataSource={mockDetail.services}
        columns={columns}
        pagination={false}
        bordered
        size="small"
      />
    </Card>
  )
}

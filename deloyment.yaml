# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-crm-admin-dev
  namespace: ape-crm-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-crm-admin-dev
  template:
    metadata:
      labels:
        app: ape-crm-admin-dev
    spec:
      containers:
        - name: ape-crm-admin-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-crm-admin-dev:latest
          ports:
            - containerPort: 80
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

import React from "react";
import {
  Button,
  Form,
  Input,
  Layout,
  Spin,
  Typography,
  ConfigProvider,
  theme,
  Space,
} from "antd";
import { useAuthStore } from "~/stores/authStore";
import { COLORS } from "~/common/constants";
import { loginAssets } from "~/assets";
import BaseCheckbox from "~/components/BaseCheckbox";
import { TEXT_SIZE } from "~/common/constants/size";
import { configEnv } from "~/@config";

const { Title } = Typography;

// TypeScript interfaces
interface LoginFormValues {
  email: string;
  password: string;
  remember?: boolean;
}

// Light theme config cố định cho login
const loginThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: COLORS.PRIMARY_RGB,
    colorBgContainer: COLORS.WHITE,
    colorBgBase: COLORS.WHITE,
    colorText: COLORS.BLACK,
    colorTextBase: COLORS.BLACK,
  },
  components: {
    Layout: {
      colorBgHeader: COLORS.WHITE,
      colorBgBody: COLORS.WHITE,
    },
    Input: {
      colorBgContainer: COLORS.WHITE,
      colorText: COLORS.BLACK,
    },
    Button: {
      colorPrimary: COLORS.PRIMARY_2,
    },
    Form: {
      labelColor: COLORS.BLACK,
    },
  },
};

const LoginView: React.FC = () => {
  const { login, isLoading } = useAuthStore();

  const handleFinish = (values: LoginFormValues) => {
    login({
      email: values.email,
      password: values.password,
    });
  };

  const handleLoginApe = () => {
    const { baseUrl } = configEnv().CONNECTORS.ROOT;
    const redirectUri = encodeURIComponent(
      `${window.location.origin}/ape-callback`
    );
    window.location.href = `${baseUrl}/api/client/auth/ape?redirectUri=${redirectUri}`;
  };

  return (
    <ConfigProvider theme={loginThemeConfig}>
      <Layout style={styles.layout}>
        <Spin tip="Đang đăng nhập..." spinning={isLoading}>
          <div style={styles.formContainer}>
            <Title style={styles.title}>Chào mừng trở lại</Title>
            <Space style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
              <Button type="primary" onClick={handleLoginApe}>Login by APE</Button>
              </Space>
            
            <Form<LoginFormValues>
              name="loginForm"
              layout="vertical"
              initialValues={{ remember: true }}
              onFinish={handleFinish}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                label="Tài khoản"
                name="username"
                style={styles.formItem}
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập tài khoản của bạn",
                  },
                  {
                    min: 3,
                    message: "Tài khoản phải có ít nhất 3 ký tự",
                  },
                ]}
              >
                <Input placeholder="Nhập tài khoản của bạn" />
              </Form.Item>

              <Form.Item
                label="Mật khẩu"
                name="password"
                rules={[
                  {
                    required: true,
                    message: "Vui lòng nhập mật khẩu của bạn",
                  },
                  {
                    min: 6,
                    message: "Mật khẩu phải có ít nhất 6 ký tự",
                  },
                ]}
              >
                <Input.Password placeholder="Nhập mật khẩu của bạn" />
              </Form.Item>

              <Form.Item name="remember" valuePropName="checked">
                <BaseCheckbox
                  label="Ghi nhớ đăng nhập"
                  checked={true}
                  onChange={() => {}}
                  sizeCheckbox="large"
                  sizeText="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  style={styles.submitButton}
                  loading={isLoading}
                >
                  Đăng nhập
                </Button>
              </Form.Item>
            </Form>
          </div>
        </Spin>
      </Layout>
    </ConfigProvider>
  );
};

export default LoginView;

// Styles với màu cố định
const styles: Record<string, React.CSSProperties> = {
  layout: {
    height: "100vh",
    justifyContent: "center",
    alignItems: "flex-end",
    backgroundImage: `url(${loginAssets.loginBackground})`,
    backgroundSize: "cover",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center",
    paddingRight: 100,
    backgroundColor: COLORS.WHITE, // Đảm bảo background luôn trắng
  },
  formContainer: {
    backgroundColor: COLORS.WHITE,
    padding: "20px 24px",
    borderRadius: 12,
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
    width: 360,
    color: COLORS.BLACK, // Đảm bảo text luôn đen
  },
  title: {
    textAlign: "center",
    marginBottom: 24,
    color: COLORS.PRIMARY_2,
    fontSize: "30px",
  },
  submitButton: {
    backgroundColor: COLORS.PRIMARY_2,
    borderColor: COLORS.PRIMARY_2,
    width: "100%",
    height: 40,
  },
  formItem: {
    marginBottom: 16,
  },
};

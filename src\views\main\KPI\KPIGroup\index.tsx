import BaseView from '~/components/BaseView'
import { useTranslation } from 'react-i18next'
import { IKpiGroupFilter, IKpiGroup } from '~/dto/Kpi.dto'
import { Tag } from 'antd'
import { ColumnsType } from 'antd/es/table'
import BaseTable from '~/components/BaseTable'
import EditButton from './components/EditButton'
import { DeleteOutlined } from '@ant-design/icons'
import { BaseButton } from '~/components'
import DetailButton from './components/DetailButton'
import { useState } from 'react'
import FilterKPI from './components/FilterKPIGroup'
export const KPIGroupView = () => {
  const { t } = useTranslation()
  //filter
  const [filter, setFilter] = useState<IKpiGroupFilter>({
    pageIndex: 0,
    pageSize: 10
  })
  const columns: ColumnsType<IKpiGroup> = [
    { title: 'Mã', dataIndex: 'code', key: 'code', width: '5%', align: 'center' },
    { title: 'Tên <PERSON>', dataIndex: 'groupName', key: 'groupName', width: '10%',align: 'center' },
    { title: 'Phòng ban', dataIndex: 'department', key: 'department', width: '15%', align: 'center' },
    { title: 'Người tạo', dataIndex: 'createdBy', key: 'createdBy', width: '10%', align: 'center' },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: '10%',
      align: 'center'
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedDate',
      key: 'updatedDate',
      width: '10%',
      align: 'center'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      align: 'center',
      render: (value: string) => <Tag color={value === 'Hoạt động' ? 'green' : 'red'}>{value}</Tag>
    },
    {
      title: 'Tác vụ',
      key: 'action',
      width: '10%',
      align: 'center',
      render: (_, record) => (
        <>
          <EditButton data={record}></EditButton>
          <DetailButton data={record}></DetailButton>
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]
  const handleDelete = (record: IKpiGroup) => {
    setFakeData(fakeData.filter((item) => item.code !== record.code))
  }

  const [fakeData, setFakeData] = useState<IKpiGroup[]>([
    {
      code: 'KPI01',
      groupName: 'KPI NV KD',
      department: 'Kinh doanh',
      createdBy: 'ADMIN',
      createdDate: '19/06/2025',
      updatedDate: '19/06/2025',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      code: 'KPI02',
      groupName: 'KPI NV MKT',
      department: 'Marketing',
      createdBy: 'ADMIN',
      createdDate: '19/06/2025',
      updatedDate: '19/06/2025',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      code: 'KPI03',
      groupName: 'KPI NV CSKH',
      department: 'CSKH',
      createdBy: 'ADMIN',
      createdDate: '19/06/2025',
      updatedDate: '19/06/2025',
      frequency: 'tháng',
      status: 'Hoạt động'
    }
  ])

  const handleFilter = (values: IKpiGroupFilter) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFakeData(fakeData)
  }

  return (
    <BaseView>
      <FilterKPI onFilter={handleFilter} onReset={handleReset} isLoading={false} />
      <BaseTable columns={columns} data={fakeData} total={fakeData.length} isLoading={false} />
    </BaseView>
  )
}
